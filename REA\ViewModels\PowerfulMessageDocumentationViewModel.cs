using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using Microsoft.Win32;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;
using DriverManagementSystem.Views;
using System.Collections.Generic;
using System.Collections.Specialized;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel قوي ومتطور لتوثيق الرسائل النصية
    /// </summary>
    public class PowerfulMessageDocumentationViewModel : BindableBase
    {
        private readonly ApplicationDbContext _context;
        private MessageDocumentation _documentation;
        private FieldVisit _selectedVisit;
        private string _lastSelectedFolder;

        public PowerfulMessageDocumentationViewModel(FieldVisit selectedVisit)
        {
            _context = new ApplicationDbContext();
            _selectedVisit = selectedVisit;

            InitializeDocumentation();
            InitializeCommands();
            LoadExistingDocumentation();
            LoadLastSelectedFolder();
        }

        #region Properties

        public MessageDocumentation Documentation
        {
            get => _documentation;
            set => SetProperty(ref _documentation, value);
        }

        public FieldVisit SelectedVisit
        {
            get => _selectedVisit;
            set => SetProperty(ref _selectedVisit, value);
        }

        private ObservableCollection<string> _imagePaths = new ObservableCollection<string>();
        public ObservableCollection<string> ImagePaths
        {
            get => _imagePaths;
            set => SetProperty(ref _imagePaths, value);
        }



        private ObservableCollection<VisitConductorItem> _visitConductors = new ObservableCollection<VisitConductorItem>();
        public ObservableCollection<VisitConductorItem> VisitConductors
        {
            get => _visitConductors;
            set => SetProperty(ref _visitConductors, value);
        }

        private int _imagePathsCount;
        public int ImagePathsCount
        {
            get => _imagePathsCount;
            set => SetProperty(ref _imagePathsCount, value);
        }





        private string _visitConductor1;
        public string VisitConductor1
        {
            get => _visitConductor1;
            set => SetProperty(ref _visitConductor1, value);
        }

        private string _visitConductor2;
        public string VisitConductor2
        {
            get => _visitConductor2;
            set => SetProperty(ref _visitConductor2, value);
        }

        private string _visitConductor3;
        public string VisitConductor3
        {
            get => _visitConductor3;
            set => SetProperty(ref _visitConductor3, value);
        }



        private string _imagesFolderPath;
        public string ImagesFolderPath
        {
            get => _imagesFolderPath;
            set => SetProperty(ref _imagesFolderPath, value);
        }

        #endregion

        #region Commands

        public ICommand SelectImagesFolderCommand { get; private set; }
        public ICommand AddSingleImageCommand { get; private set; }
        public ICommand RemoveImageCommand { get; private set; }
        public ICommand SaveDocumentationCommand { get; private set; }


        #endregion

        private void InitializeDocumentation()
        {
            Documentation = new MessageDocumentation
            {
                VisitNumber = _selectedVisit?.VisitNumber ?? "",
                VisitConductor1 = _selectedVisit?.Visitors?.FirstOrDefault()?.OfficerName ?? "",
                DocumentationDate = DateTime.Now
            };

            // إضافة مراقبة للتغييرات في المجموعات
            ImagePaths.CollectionChanged += (s, e) =>
            {
                ImagePathsCount = ImagePaths.Count;
            };

            // تحديث العدادات الأولية
            ImagePathsCount = ImagePaths.Count;
        }

        private void InitializeCommands()
        {
            SelectImagesFolderCommand = new DelegateCommand(SelectImagesFolder);
            AddSingleImageCommand = new DelegateCommand(AddSingleImage);
            RemoveImageCommand = new DelegateCommand<string>(RemoveImage);
            SaveDocumentationCommand = new DelegateCommand(SaveDocumentation);
        }



        /// <summary>
        /// اختيار مجلد الصور مع حفظ المسار
        /// </summary>
        private void SelectImagesFolder()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختيار مجلد الصور - اختر أي ملف في المجلد المطلوب",
                    Filter = "جميع الملفات (*.*)|*.*",
                    CheckFileExists = false,
                    CheckPathExists = true,
                    Multiselect = false
                };

                // استخدام آخر مجلد محفوظ
                if (!string.IsNullOrEmpty(_lastSelectedFolder) && Directory.Exists(_lastSelectedFolder))
                {
                    openFileDialog.InitialDirectory = _lastSelectedFolder;
                }

                if (openFileDialog.ShowDialog() == true)
                {
                    var folderPath = Path.GetDirectoryName(openFileDialog.FileName);
                    if (!string.IsNullOrEmpty(folderPath))
                    {
                        ImagesFolderPath = folderPath;

                        // حفظ المسار لاستخدامه لاحقاً
                        SaveLastSelectedFolder(folderPath);

                        // تحميل الصور من المجلد
                        LoadImagesFromFolder(folderPath);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار مجلد الصور: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل الصور من المجلد المحدد مع معاينة مباشرة
        /// </summary>
        private void LoadImagesFromFolder(string folderPath)
        {
            try
            {
                ImagePaths.Clear();
                
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".gif" };
                var imageFiles = Directory.GetFiles(folderPath)
                    .Where(file => allowedExtensions.Contains(Path.GetExtension(file).ToLower()))
                    .Take(4) // أقصى 4 صور
                    .ToList();

                foreach (var imagePath in imageFiles)
                {
                    ImagePaths.Add(imagePath);
                }

                // تحديث مسارات الصور في النموذج
                UpdateImagePathsInModel();

                MessageBox.Show($"تم تحميل {ImagePaths.Count} صورة من المجلد", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصور: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إضافة صورة واحدة مع معاينة فورية
        /// </summary>
        private void AddSingleImage()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختيار صورة",
                    Filter = "ملفات الصور (*.jpg;*.jpeg;*.png;*.bmp;*.gif)|*.jpg;*.jpeg;*.png;*.bmp;*.gif",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    if (ImagePaths.Count < 4)
                    {
                        ImagePaths.Add(openFileDialog.FileName);
                        UpdateImagePathsInModel();
                    }
                    else
                    {
                        MessageBox.Show("لا يمكن إضافة أكثر من 4 صور", "تنبيه",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الصورة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث مسارات الصور في النموذج
        /// </summary>
        private void UpdateImagePathsInModel()
        {
            Documentation.ImagePath1 = ImagePaths.Count > 0 ? ImagePaths[0] : null;
            Documentation.ImagePath2 = ImagePaths.Count > 1 ? ImagePaths[1] : null;
            Documentation.ImagePath3 = ImagePaths.Count > 2 ? ImagePaths[2] : null;
            Documentation.ImagePath4 = ImagePaths.Count > 3 ? ImagePaths[3] : null;
            Documentation.ImagePath5 = null; // لا نستخدم الصورة الخامسة
            Documentation.ImagePath6 = null; // لا نستخدم الصورة السادسة
            Documentation.ImagesCount = ImagePaths.Count;
            Documentation.ImagesFolderPath = ImagesFolderPath;

            // تحديث العداد
            ImagePathsCount = ImagePaths.Count;
        }

        private void RemoveImage(string imagePath)
        {
            if (!string.IsNullOrEmpty(imagePath) && ImagePaths.Contains(imagePath))
            {
                ImagePaths.Remove(imagePath);
                UpdateImagePathsInModel();
            }
        }



        /// <summary>
        /// تحميل بيانات FieldVisit مع العلاقات من قاعدة البيانات
        /// </summary>
        private void LoadFieldVisitWithRelations()
        {
            try
            {
                if (_selectedVisit != null)
                {
                    // تحميل FieldVisit مع Visitors من قاعدة البيانات
                    var visitWithRelations = _context.FieldVisits
                        .Where(v => v.Id == _selectedVisit.Id)
                        .Select(v => new FieldVisit
                        {
                            Id = v.Id,
                            VisitNumber = v.VisitNumber,
                            Visitors = _context.FieldVisitors
                                .Where(fv => fv.FieldVisitId == v.Id)
                                .ToList()
                        })
                        .FirstOrDefault();

                    if (visitWithRelations != null && visitWithRelations.Visitors.Any())
                    {
                        _selectedVisit.Visitors = visitWithRelations.Visitors;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات الزيارة: {ex.Message}");
            }
        }

        /// <summary>
        /// جلب بيانات القائمين بالزيارة من FieldVisit
        /// </summary>
        private void LoadVisitConductorsFromFieldVisit()
        {
            VisitConductors.Clear();

            if (_selectedVisit != null)
            {
                // جلب أسماء القائمين بالزيارة من قائمة Visitors
                if (_selectedVisit.Visitors != null && _selectedVisit.Visitors.Any())
                {
                    var visitors = _selectedVisit.Visitors.ToArray();

                    // إنشاء صفوف ديناميكية حسب عدد القائمين بالزيارة
                    for (int i = 0; i < visitors.Length; i++)
                    {
                        var conductor = new VisitConductorItem
                        {
                            Label = $"القائم {GetOrdinalNumber(i + 1)}:",
                            Name = visitors[i].OfficerName ?? visitors[i].Name
                        };
                        VisitConductors.Add(conductor);
                    }

                    // تعبئة الخصائص القديمة للتوافق مع الحفظ
                    if (visitors.Length > 0) VisitConductor1 = visitors[0].OfficerName ?? visitors[0].Name;
                    if (visitors.Length > 1) VisitConductor2 = visitors[1].OfficerName ?? visitors[1].Name;
                    if (visitors.Length > 2) VisitConductor3 = visitors[2].OfficerName ?? visitors[2].Name;
                }
            }
        }

        /// <summary>
        /// تحويل الرقم إلى ترتيب عربي
        /// </summary>
        private string GetOrdinalNumber(int number)
        {
            return number switch
            {
                1 => "الأول",
                2 => "الثاني",
                3 => "الثالث",
                4 => "الرابع",
                5 => "الخامس",
                _ => $"الـ{number}"
            };
        }

        private void LoadExistingDocumentation()
        {
            try
            {
                // التحقق من وجود زيارة محددة
                if (_selectedVisit == null) return;

                // تحميل بيانات FieldVisit مع العلاقات
                LoadFieldVisitWithRelations();

                // جلب بيانات القائمين بالزيارة من FieldVisit
                LoadVisitConductorsFromFieldVisit();

                var existing = _context.MessageDocumentations
                    .FirstOrDefault(d => d.VisitNumber == _selectedVisit.VisitNumber);

                if (existing != null)
                {
                    // تحديث البيانات الموجودة بدلاً من استبدالها
                    Documentation.Id = existing.Id;
                    Documentation.VisitConductor1 = existing.VisitConductor1;
                    Documentation.VisitConductor2 = existing.VisitConductor2;
                    Documentation.VisitConductor3 = existing.VisitConductor3;
                    Documentation.Notes = existing.Notes;
                    Documentation.Status = existing.Status;
                    Documentation.ImagesFolderPath = existing.ImagesFolderPath;
                    Documentation.ImagePath1 = existing.ImagePath1;
                    Documentation.ImagePath2 = existing.ImagePath2;
                    Documentation.ImagePath3 = existing.ImagePath3;
                    Documentation.ImagePath4 = existing.ImagePath4;
                    Documentation.DocumentationDate = existing.DocumentationDate;
                    Documentation.LastModified = existing.LastModified;

                    // تحديث الخصائص في ViewModel
                    if (!string.IsNullOrEmpty(existing.VisitConductor1))
                        VisitConductor1 = existing.VisitConductor1;
                    if (!string.IsNullOrEmpty(existing.VisitConductor2))
                        VisitConductor2 = existing.VisitConductor2;
                    if (!string.IsNullOrEmpty(existing.VisitConductor3))
                        VisitConductor3 = existing.VisitConductor3;
                    ImagesFolderPath = existing.ImagesFolderPath;

                    // تحميل مسارات الصور
                    LoadImagePathsFromModel();

                    // إشعار الواجهة بالتحديث
                    RaisePropertyChanged(nameof(Documentation));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل التوثيق الموجود: {ex.Message}");
            }
        }

        private void LoadImagePathsFromModel()
        {
            ImagePaths.Clear();

            if (!string.IsNullOrEmpty(Documentation.ImagePath1)) ImagePaths.Add(Documentation.ImagePath1);
            if (!string.IsNullOrEmpty(Documentation.ImagePath2)) ImagePaths.Add(Documentation.ImagePath2);
            if (!string.IsNullOrEmpty(Documentation.ImagePath3)) ImagePaths.Add(Documentation.ImagePath3);
            if (!string.IsNullOrEmpty(Documentation.ImagePath4)) ImagePaths.Add(Documentation.ImagePath4);
            // لا نحمل الصورة الخامسة والسادسة

            // تحديث العداد
            ImagePathsCount = ImagePaths.Count;
        }

        private void LoadLastSelectedFolder()
        {
            try
            {
                _lastSelectedFolder = Microsoft.Win32.Registry.GetValue(
                    @"HKEY_CURRENT_USER\Software\SFDSystem\MessageDocumentation", 
                    "LastFolder", 
                    "") as string;
            }
            catch
            {
                _lastSelectedFolder = "";
            }
        }

        private void SaveLastSelectedFolder(string folderPath)
        {
            try
            {
                Microsoft.Win32.Registry.SetValue(
                    @"HKEY_CURRENT_USER\Software\SFDSystem\MessageDocumentation", 
                    "LastFolder", 
                    folderPath);
                _lastSelectedFolder = folderPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ مسار المجلد: {ex.Message}");
            }
        }

        private void SaveDocumentation()
        {
            try
            {
                // حفظ بيانات القائمين بالزيارة من المجموعة الديناميكية
                if (VisitConductors.Count > 0) Documentation.VisitConductor1 = VisitConductors[0].Name;
                if (VisitConductors.Count > 1) Documentation.VisitConductor2 = VisitConductors[1].Name;
                if (VisitConductors.Count > 2) Documentation.VisitConductor3 = VisitConductors[2].Name;
                Documentation.LastModified = DateTime.Now;

                // تحديث مسارات الصور
                UpdateImagePathsInModel();

                // التحقق من وجود سجل موجود
                var existing = _context.MessageDocumentations
                    .FirstOrDefault(d => d.VisitNumber == Documentation.VisitNumber);

                if (existing != null)
                {
                    // تحديث السجل الموجود بدون تعديل الـ Id
                    existing.VisitNumber = Documentation.VisitNumber;
                    existing.VisitConductor1 = Documentation.VisitConductor1;
                    existing.VisitConductor2 = Documentation.VisitConductor2;
                    existing.VisitConductor3 = Documentation.VisitConductor3;
                    existing.DocumentationDate = Documentation.DocumentationDate;
                    existing.LastModified = Documentation.LastModified;
                    existing.Notes = Documentation.Notes;
                    existing.Status = Documentation.Status;
                    existing.ImagesFolderPath = Documentation.ImagesFolderPath;
                    existing.ImagePath1 = Documentation.ImagePath1;
                    existing.ImagePath2 = Documentation.ImagePath2;
                    existing.ImagePath3 = Documentation.ImagePath3;
                    existing.ImagePath4 = Documentation.ImagePath4;
                }
                else
                {
                    // إضافة سجل جديد (بدون تحديد Id - سيتم إنشاؤه تلقائياً)
                    var newDoc = new MessageDocumentation
                    {
                        VisitNumber = Documentation.VisitNumber,
                        VisitConductor1 = Documentation.VisitConductor1,
                        VisitConductor2 = Documentation.VisitConductor2,
                        VisitConductor3 = Documentation.VisitConductor3,
                        DocumentationDate = Documentation.DocumentationDate,
                        LastModified = Documentation.LastModified,
                        Notes = Documentation.Notes,
                        Status = Documentation.Status,
                        ImagesFolderPath = Documentation.ImagesFolderPath,
                        ImagePath1 = Documentation.ImagePath1,
                        ImagePath2 = Documentation.ImagePath2,
                        ImagePath3 = Documentation.ImagePath3,
                        ImagePath4 = Documentation.ImagePath4
                    };
                    _context.MessageDocumentations.Add(newDoc);
                }

                _context.SaveChanges();

                MessageBox.Show("✅ تم حفظ التوثيق بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في حفظ التوثيق: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// عنصر القائم بالزيارة
    /// </summary>
    public class VisitConductorItem : BindableBase
    {
        private string _name = string.Empty;
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Label { get; set; } = string.Empty;
    }
}
