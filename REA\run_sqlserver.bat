@echo off
echo ========================================
echo    🚀 تشغيل نظام إدارة الزيارات الميدانية
echo    📊 SQL Server حصرياً
echo ========================================
echo.

echo 📦 جاري بناء المشروع...
dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo 🔧 إعداد متغيرات البيئة لـ SQL Server...
set SQL_SERVER_NAME=localhost
set SQL_DATABASE_NAME=SFDSYS
set SQL_USE_WINDOWS_AUTH=true

echo 🎯 جاري تشغيل النظام...
echo ℹ️  النظام يعمل حصرياً مع SQL Server
echo.

dotnet run --configuration Release

echo.
echo 📝 تم إغلاق النظام
pause
