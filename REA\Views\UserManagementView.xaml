<UserControl x:Class="DriverManagementSystem.Views.UserManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5,0"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="👥" FontSize="24" Foreground="White" Margin="0,0,15,0" VerticalAlignment="Center"/>
                <StackPanel>
                    <TextBlock Text="إدارة المستخدمين"
                             FontSize="20"
                             FontWeight="Bold"
                             Foreground="White"/>
                    <TextBlock Text="إضافة وتعديل وإدارة صلاحيات المستخدمين"
                             FontSize="14"
                             Foreground="White"
                             Opacity="0.8"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Toolbar -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="20" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="AddUserButton"
                        Content="➕ إضافة مستخدم جديد"
                        Background="#4CAF50"
                        Foreground="White"
                        Style="{StaticResource ActionButtonStyle}"
                        Padding="15,8"
                        Click="AddUserButton_Click"/>
                
                <Button x:Name="RefreshButton" 
                        Content="🔄 تحديث"
                        Background="#6C757D"
                        Foreground="White"
                        Style="{StaticResource ActionButtonStyle}"
                        Click="RefreshButton_Click"/>

                <TextBox x:Name="SearchTextBox"
                         Width="200"
                         Margin="20,0,0,0"
                         Padding="8"
                         BorderBrush="#CCC"
                         BorderThickness="1"
                         Text="🔍 البحث في المستخدمين..."
                         Foreground="#999"
                         GotFocus="SearchTextBox_GotFocus"
                         LostFocus="SearchTextBox_LostFocus"
                         TextChanged="SearchTextBox_TextChanged"/>
            </StackPanel>
        </Border>

        <!-- Users List -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <StackPanel x:Name="UsersPanel" Margin="20">
                <!-- Users will be loaded dynamically -->
                <TextBlock x:Name="NoUsersMessage"
                          Text="لا توجد مستخدمين للعرض"
                          FontSize="16"
                          Foreground="#666"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Margin="50"
                          Visibility="Collapsed"/>

                <TextBlock x:Name="LoadingMessage"
                          Text="جاري تحميل المستخدمين..."
                          FontSize="16"
                          Foreground="#666"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Margin="50"/>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
