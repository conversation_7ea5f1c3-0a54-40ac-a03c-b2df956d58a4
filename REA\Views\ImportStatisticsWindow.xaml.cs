using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة إحصائيات استيراد Excel المتقدمة
    /// </summary>
    public partial class ImportStatisticsWindow : Window, INotifyPropertyChanged
    {
        private readonly ImportLogService _importLogService;
        private ImportStatistics _statistics = new();
        private ObservableCollection<ImportLogEntry> _recentImports = new();
        private DateTime _lastUpdateTime = DateTime.Now;

        public ImportStatistics Statistics
        {
            get => _statistics;
            set
            {
                _statistics = value;
                OnPropertyChanged(nameof(Statistics));
            }
        }

        public ObservableCollection<ImportLogEntry> RecentImports
        {
            get => _recentImports;
            set
            {
                _recentImports = value;
                OnPropertyChanged(nameof(RecentImports));
            }
        }

        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                _lastUpdateTime = value;
                OnPropertyChanged(nameof(LastUpdateTime));
            }
        }

        public ImportStatisticsWindow()
        {
            InitializeComponent();
            _importLogService = new ImportLogService();
            DataContext = this;
            
            // تحميل البيانات عند فتح النافذة
            Loaded += async (s, e) => await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                // عرض مؤشر التحميل
                Cursor = System.Windows.Input.Cursors.Wait;

                // تحميل الإحصائيات
                Statistics = await _importLogService.GetImportStatisticsAsync();

                // تحميل آخر العمليات
                var recentImports = await _importLogService.GetRecentImportsAsync(50);
                RecentImports.Clear();
                foreach (var import in recentImports)
                {
                    RecentImports.Add(import);
                }

                LastUpdateTime = DateTime.Now;

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل إحصائيات الاستيراد: {Statistics.TotalImports} عملية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل إحصائيات الاستيراد: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل البيانات:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
            
            // إظهار رسالة تأكيد
            MessageBox.Show("✅ تم تحديث البيانات بنجاح", 
                          "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void ClearLogButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "⚠️ هل أنت متأكد من مسح جميع سجلات الاستيراد؟\n\nهذا الإجراء لا يمكن التراجع عنه!",
                    "تأكيد المسح", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    await _importLogService.ClearLogsAsync();
                    await LoadDataAsync();
                    
                    MessageBox.Show("✅ تم مسح سجل الاستيراد بنجاح", 
                                  "تم المسح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في مسح سجل الاستيراد: {ex.Message}");
                MessageBox.Show($"خطأ في مسح السجل:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
