using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة إدارة عروض الأسعار
    /// </summary>
    public interface IOffersService
    {
        /// <summary>
        /// حفظ عروض الأسعار للزيارة
        /// </summary>
        /// <param name="visitId">معرف الزيارة</param>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="selectedOffers">العروض المختارة</param>
        /// <returns>نجح الحفظ أم لا</returns>
        Task<bool> SaveOffersAsync(int visitId, string visitNumber, List<DriverOffer> selectedOffers);

        /// <summary>
        /// حفظ عروض الأسعار كنص
        /// </summary>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="offersText">نص العروض</param>
        /// <param name="daysCount">عدد الأيام</param>
        /// <returns>نجح الحفظ أم لا</returns>
        Task<bool> SaveOffersTextAsync(string visitNumber, string offersText, int daysCount);

        /// <summary>
        /// الحصول على عروض الأسعار للزيارة
        /// </summary>
        /// <param name="visitId">معرف الزيارة</param>
        /// <returns>قائمة العروض</returns>
        Task<List<DriverOffer>> GetVisitOffersAsync(int visitId);

        /// <summary>
        /// الحصول على عروض الأسعار بالرقم
        /// </summary>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <returns>قائمة العروض</returns>
        Task<List<DriverOffer>> GetVisitOffersByNumberAsync(string visitNumber);

        /// <summary>
        /// حذف عروض الأسعار للزيارة
        /// </summary>
        /// <param name="visitId">معرف الزيارة</param>
        /// <returns>نجح الحذف أم لا</returns>
        Task<bool> DeleteVisitOffersAsync(int visitId);

        /// <summary>
        /// الحصول على إحصائيات العروض
        /// </summary>
        /// <param name="visitId">معرف الزيارة</param>
        /// <returns>إحصائيات العروض</returns>
        Task<OffersStatistics> GetOffersStatisticsAsync(int visitId);

        /// <summary>
        /// البحث في العروض
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <returns>قائمة العروض المطابقة</returns>
        Task<List<DriverOffer>> SearchOffersAsync(string searchText);

        /// <summary>
        /// الحصول على أفضل عرض للزيارة
        /// </summary>
        /// <param name="visitId">معرف الزيارة</param>
        /// <returns>أفضل عرض</returns>
        Task<DriverOffer> GetBestOfferAsync(int visitId);

        /// <summary>
        /// تحديث حالة العرض
        /// </summary>
        /// <param name="offerId">معرف العرض</param>
        /// <param name="isSelected">حالة الاختيار</param>
        /// <param name="isWinner">حالة الفوز</param>
        /// <returns>نجح التحديث أم لا</returns>
        Task<bool> UpdateOfferStatusAsync(int offerId, bool isSelected, bool isWinner);

        /// <summary>
        /// إنشاء تقرير العروض
        /// </summary>
        /// <param name="visitId">معرف الزيارة</param>
        /// <returns>تقرير العروض</returns>
        Task<OffersReport> GenerateOffersReportAsync(int visitId);
    }

    /// <summary>
    /// إحصائيات العروض
    /// </summary>
    public class OffersStatistics
    {
        public int TotalOffers { get; set; }
        public int SelectedOffers { get; set; }
        public decimal LowestPrice { get; set; }
        public decimal HighestPrice { get; set; }
        public decimal AveragePrice { get; set; }
        public decimal TotalSelectedAmount { get; set; }
        public string WinnerDriverName { get; set; } = string.Empty;
        public decimal WinnerAmount { get; set; }
        public DateTime LastUpdateDate { get; set; }
    }

    /// <summary>
    /// تقرير العروض
    /// </summary>
    public class OffersReport
    {
        public string VisitNumber { get; set; } = string.Empty;
        public DateTime ReportDate { get; set; }
        public int DaysCount { get; set; }
        public List<DriverOffer> AllOffers { get; set; } = new();
        public List<DriverOffer> SelectedOffers { get; set; } = new();
        public DriverOffer WinnerOffer { get; set; }
        public OffersStatistics Statistics { get; set; } = new();
        public string Summary { get; set; } = string.Empty;
        public string RecommendationNotes { get; set; } = string.Empty;
    }
}
