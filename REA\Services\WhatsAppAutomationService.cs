using System;
using System.Threading.Tasks;
using System.Windows;
using System.IO;
using System.Diagnostics;

namespace SFDSystem.Services
{
    /// <summary>
    /// خدمة فتح الواتس اب Desktop مع الرسالة جاهزة (إرسال يدوي مطلوب)
    /// </summary>
    public class WhatsAppAutomationService
    {
        /// <summary>
        /// فتح WhatsApp Desktop مع الرسالة جاهزة (الإرسال الفعلي يحتاج تدخل يدوي)
        /// </summary>
        public async Task<bool> SendMessageAutomaticallyAsync(string phoneNumber, string message)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🖥️ Opening WhatsApp Desktop for {phoneNumber} - manual completion required");

                // تنظيف رقم الهاتف
                string cleanPhoneNumber = CleanPhoneNumber(phoneNumber);
                if (string.IsNullOrEmpty(cleanPhoneNumber))
                {
                    throw new ArgumentException("رقم الهاتف غير صحيح");
                }

                // فتح WhatsApp Desktop مع الرسالة جاهزة
                return await SendViaWhatsAppDesktop(cleanPhoneNumber, message);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error opening WhatsApp Desktop: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// فتح WhatsApp Desktop مع الرسالة جاهزة
        /// </summary>
        private async Task<bool> SendViaWhatsAppDesktop(string phoneNumber, string message)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖥️ Opening WhatsApp Desktop with message ready...");

                // إنشاء رابط WhatsApp Desktop
                string whatsappDesktopUrl = $"whatsapp://send?phone={phoneNumber}&text={Uri.EscapeDataString(message)}";
                System.Diagnostics.Debug.WriteLine($"🔗 Opening: {whatsappDesktopUrl}");

                // فتح WhatsApp Desktop
                Process.Start(new ProcessStartInfo
                {
                    FileName = whatsappDesktopUrl,
                    UseShellExecute = true
                });

                // انتظار قصير لفتح التطبيق
                await Task.Delay(1500);

                System.Diagnostics.Debug.WriteLine("✅ WhatsApp Desktop opened - manual sending required");
                return true; // نعيد true لأن التطبيق فُتح، الإرسال الفعلي يحتاج تدخل يدوي
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error opening WhatsApp Desktop: {ex.Message}");
                return false;
            }
        }

        // تم إزالة الإرسال التلقائي المضلل - الإرسال اليدوي فقط

        /// <summary>
        /// تنظيف رقم الهاتف
        /// </summary>
        private string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return string.Empty;

            // إزالة المسافات والرموز غير المرغوبة
            string cleaned = phoneNumber.Replace(" ", "")
                                      .Replace("-", "")
                                      .Replace("(", "")
                                      .Replace(")", "")
                                      .Replace("+", "");

            // إضافة رمز السعودية إذا لم يكن موجوداً
            if (!cleaned.StartsWith("966") && cleaned.StartsWith("05"))
            {
                cleaned = "966" + cleaned.Substring(1);
            }
            else if (!cleaned.StartsWith("966") && cleaned.StartsWith("5"))
            {
                cleaned = "966" + cleaned;
            }

            return cleaned;
        }
    }
}
