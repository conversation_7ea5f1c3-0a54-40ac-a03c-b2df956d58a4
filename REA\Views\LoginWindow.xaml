<Window x:Class="DriverManagementSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - نظام إدارة الزيارات الميدانية"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Background="#F8F9FA"
        FlowDirection="RightToLeft"
>

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Assets/SystemLogo.xaml"/>
                <ResourceDictionary Source="../Styles/LoginStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>



    <!-- Scrollable Content -->
    <ScrollViewer VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="20" Height="787">
        <!-- Login Card -->
        <Border Background="White"
                CornerRadius="12"
                Padding="30"
                Margin="20"
                MaxWidth="400"
                MinWidth="350" Height="701">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="15"/>
            </Border.Effect>
            <StackPanel>
                <!-- Digital Clock -->
                <Border Background="#E8F5E8"
                        CornerRadius="6"
                        Padding="12,8"
                        Margin="0,0,0,20">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="🕐" FontSize="16" Margin="0,0,10,0" VerticalAlignment="Center"/>
                        <TextBlock x:Name="ClockText"
                                 Text="الأحد، 15 ديسمبر 2024 - 02:30:45 مساءً"
                                 FontSize="13"
                                 FontWeight="Medium"
                                 Foreground="#2E7D32"
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Header without Logo -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,25">
                    <TextBlock Text="نظام إدارة السائقين"
                             FontSize="24"
                             FontWeight="Bold"
                             Foreground="{StaticResource TextPrimaryBrush}"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,8"/>
                    <TextBlock Text="Driver Management System"
                             FontSize="14"
                             Foreground="{StaticResource TextSecondaryBrush}"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,8"/>
                    <TextBlock Text="مرحباً بك، يرجى تسجيل الدخول"
                             FontSize="16"
                             Foreground="{StaticResource TextSecondaryBrush}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Username Field -->
                <StackPanel Margin="0,0,0,18">
                    <TextBlock Text="اسم المستخدم"
                             FontWeight="Medium"
                             FontSize="15"
                             Foreground="{StaticResource TextPrimaryBrush}"
                             Margin="0,0,0,10"/>
                    <Border BorderBrush="{StaticResource TextHintBrush}"
                            BorderThickness="1"
                            CornerRadius="8"
                            Background="White">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Border Grid.Column="0"
                                    Background="{StaticResource PrimaryLightBrush}"
                                    Width="50"
                                    CornerRadius="8,0,0,8">
                                <TextBlock Text="👤"
                                         FontSize="20"
                                         HorizontalAlignment="Center"
                                         VerticalAlignment="Center"
                                         Foreground="{StaticResource PrimaryBrush}"/>
                            </Border>
                            <TextBox x:Name="UsernameTextBox"
                                   Grid.Column="1"
                                   Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                                   FontSize="15"
                                   Height="50"
                                   Padding="15,0"
                                   BorderThickness="0"
                                   Background="Transparent"
                                   VerticalContentAlignment="Center">
                                <TextBox.InputBindings>
                                    <KeyBinding Key="Enter" Command="{Binding LoginCommand}"/>
                                </TextBox.InputBindings>
                            </TextBox>
                        </Grid>
                    </Border>
                </StackPanel>

                <!-- Password Field -->
                <StackPanel Margin="0,0,0,25">
                    <TextBlock Text="كلمة المرور"
                             FontWeight="Medium"
                             FontSize="15"
                             Foreground="{StaticResource TextPrimaryBrush}"
                             Margin="0,0,0,10"/>
                    <Border BorderBrush="{StaticResource TextHintBrush}"
                            BorderThickness="1"
                            CornerRadius="8"
                            Background="White">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Border Grid.Column="0"
                                    Background="{StaticResource PrimaryLightBrush}"
                                    Width="50"
                                    CornerRadius="8,0,0,8">
                                <TextBlock Text="🔒"
                                         FontSize="20"
                                         HorizontalAlignment="Center"
                                         VerticalAlignment="Center"
                                         Foreground="{StaticResource PrimaryBrush}"/>
                            </Border>
                            <PasswordBox x:Name="PasswordBox"
                                       Grid.Column="1"
                                       FontSize="15"
                                       Height="50"
                                       Padding="15,0"
                                       BorderThickness="0"
                                       Background="Transparent"
                                       VerticalContentAlignment="Center">
                                <PasswordBox.InputBindings>
                                    <KeyBinding Key="Enter" Command="{Binding LoginCommand}"/>
                                </PasswordBox.InputBindings>
                            </PasswordBox>
                        </Grid>
                    </Border>
                </StackPanel>

                <!-- Error Message -->
                <Border Background="#FFEBEE"
                    BorderBrush="{StaticResource ErrorBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Padding="10"
                    Margin="0,0,0,15"
                    Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⚠️" FontSize="14" Margin="0,0,8,0" VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding ErrorMessage}"
                             Foreground="{StaticResource ErrorBrush}"
                             FontSize="12"
                             TextWrapping="Wrap"
                             VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>
                <Button x:Name="MainLoginButton"
                    Background="#2196F3"
                    Foreground="White"
                    FontSize="18"
                    FontWeight="Bold"
                    Height="60"
                    BorderThickness="0"
                    Cursor="Hand"
                    Click="LoginButton_Click">
                    <Button.Style>
                        <Style TargetType="{x:Type Button}">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="{x:Type Button}">
                                        <Border Background="{TemplateBinding Background}"
                                            CornerRadius="12"
                                            Padding="20,15">
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <TextBlock Text="🚀" FontSize="20" Margin="0,0,10,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="دخول النظام" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#1976D2"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#1565C0"/>
                                            </Trigger>
                                            <Trigger Property="IsEnabled" Value="False">
                                                <Setter Property="Background" Value="#BDBDBD"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- Main Login Button -->

                <!-- Loading Indicator -->
                <StackPanel x:Name="LoadingPanel"
                        Orientation="Horizontal"
                        HorizontalAlignment="Center"
                        Margin="0,10,0,0"
                        Visibility="Collapsed">
                    <TextBlock Text="⏳" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                    <TextBlock Text="جاري تسجيل الدخول..."
                         FontSize="14"
                         Foreground="{StaticResource PrimaryBrush}"
                         VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Last Login Info -->
                <Border Background="#E3F2FD"
                    CornerRadius="8"
                    Padding="12"
                    Margin="0,15,0,0">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,5">
                            <TextBlock Text="🕒" FontSize="14" Margin="0,0,8,0" VerticalAlignment="Center"/>
                            <TextBlock Text="آخر دخول"
                                 FontWeight="Medium"
                                 FontSize="13"
                                 Foreground="#1976D2"
                                 VerticalAlignment="Center"/>
                        </StackPanel>
                        <TextBlock x:Name="LastLoginText"
                             Text="الأحد، 15 ديسمبر 2024 - 02:30 مساءً"
                             FontSize="12"
                             Foreground="#424242"
                             HorizontalAlignment="Center"/>
                        <TextBlock Text="من جهاز: Windows PC"
                             FontSize="11"
                             Foreground="#757575"
                             HorizontalAlignment="Center"
                             Margin="0,3,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Quick Actions -->
                <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Center"
                        Margin="0,15,0,0">
                    <!-- Remember Me -->
                    <Border Background="#F5F5F5"
                        CornerRadius="6"
                        Padding="8,6"
                        Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <CheckBox x:Name="RememberMeCheckBox"
                                VerticalAlignment="Center"
                                IsChecked="True"/>
                            <TextBlock Text="تذكرني"
                                 FontSize="12"
                                 Foreground="#424242"
                                 VerticalAlignment="Center"
                                 Margin="5,0,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Forgot Password -->
                    <Border Background="#FFF3E0"
                        CornerRadius="6"
                        Padding="8,6"
                        Margin="8,0,0,0"
                        Cursor="Hand">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔐" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                            <TextBlock Text="نسيت كلمة المرور؟"
                                 FontSize="12"
                                 Foreground="#F57C00"
                                 VerticalAlignment="Center"
                                 TextDecorations="Underline"/>
                        </StackPanel>
                    </Border>
                </StackPanel>


            </StackPanel>
        </Border>
    </ScrollViewer>
</Window>
