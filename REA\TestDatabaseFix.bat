@echo off
echo ========================================
echo      اختبار إصلاح مشكلة تفريغ البيانات
echo ========================================
echo.

echo 🔍 فحص الملفات المعدلة...
if exist "App.xaml.cs" (
    echo ✅ تم العثور على App.xaml.cs
) else (
    echo ❌ لم يتم العثور على App.xaml.cs
    pause
    exit /b 1
)

echo.
echo 🔧 بناء المشروع...
dotnet build

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء المشروع بنجاح
    echo.
    echo 🚀 تشغيل النظام للاختبار...
    echo.
    echo ⚠️ ملاحظة: تأكد من مراقبة رسائل Debug للتحقق من:
    echo    - "🔍 فحص وجود جدول FieldVisitItineraries..."
    echo    - "✅ جدول FieldVisitItineraries موجود مسبقاً - لن يتم إعادة إنشاؤه"
    echo    - أو "🔧 إنشاء جدول FieldVisitItineraries..."
    echo.
    echo 📋 خطوات الاختبار:
    echo 1. أضف بعض البيانات في جدول خط السير
    echo 2. أغلق النظام
    echo 3. أعد تشغيل النظام
    echo 4. تحقق من أن البيانات ما زالت موجودة
    echo.
    pause
    
    start bin\Debug\net9.0-windows\SFDSystem.exe
) else (
    echo ❌ فشل في بناء المشروع
    echo تحقق من الأخطاء أعلاه
    pause
)
