using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    public class DataService : IDataService
    {
        private readonly DatabaseService _databaseService;

        public DataService()
        {
            _databaseService = new DatabaseService();
            System.Diagnostics.Debug.WriteLine("🗄️ DataService using SQL Server backend initialized successfully!");
        }

        // Drivers methods
        public async Task<List<Driver>> GetDriversAsync()
        {
            return await _databaseService.GetDriversAsync();
        }

        public async Task<bool> AddDriverAsync(Driver driver)
        {
            return await _databaseService.AddDriverAsync(driver);
        }

        public async Task<bool> UpdateDriverAsync(Driver driver)
        {
            return await _databaseService.UpdateDriverAsync(driver);
        }

        public async Task<bool> DeleteDriverAsync(int driverId)
        {
            return await _databaseService.DeleteDriverAsync(driverId);
        }

        // Sectors methods
        public async Task<List<Sector>> GetSectorsAsync()
        {
            return await _databaseService.GetSectorsAsync();
        }

        public async Task<bool> AddSectorAsync(Sector sector)
        {
            return await _databaseService.AddSectorAsync(sector);
        }

        public async Task<Sector?> GetSectorByCodeAsync(string sectorCode)
        {
            return await _databaseService.GetSectorByCodeAsync(sectorCode);
        }

        // Officers methods
        public async Task<List<Officer>> GetOfficersAsync()
        {
            return await _databaseService.GetOfficersAsync();
        }

        public async Task<List<Officer>> GetOfficersBySectorAsync(int sectorId)
        {
            return await _databaseService.GetOfficersBySectorAsync(sectorId);
        }

        public async Task<bool> AddOfficerAsync(Officer officer)
        {
            return await _databaseService.AddOfficerAsync(officer);
        }

        public async Task<bool> UpdateOfficerAsync(Officer officer)
        {
            return await _databaseService.UpdateOfficerAsync(officer);
        }

        public async Task<bool> DeleteOfficerAsync(int officerId)
        {
            return await _databaseService.DeleteOfficerAsync(officerId);
        }

        public async Task<Officer?> GetOfficerByCodeAsync(string officerCode)
        {
            return await _databaseService.GetOfficerByCodeAsync(officerCode);
        }

        // Vehicles methods
        public async Task<List<Vehicle>> GetVehiclesAsync()
        {
            return await _databaseService.GetVehiclesAsync();
        }

        public async Task<bool> AddVehicleAsync(Vehicle vehicle)
        {
            return await _databaseService.AddVehicleAsync(vehicle);
        }

        // Field Visits methods
        public async Task<List<FieldVisit>> GetFieldVisitsAsync()
        {
            return await _databaseService.GetFieldVisitsAsync();
        }

        public async Task<(bool Success, List<string> Errors)> AddFieldVisitAsync(FieldVisit fieldVisit)
        {
            return await _databaseService.AddFieldVisitAsync(fieldVisit);
        }

        public async Task<bool> UpdateFieldVisitAsync(FieldVisit fieldVisit)
        {
            return await _databaseService.UpdateFieldVisitAsync(fieldVisit);
        }

        public async Task<bool> DeleteFieldVisitAsync(int fieldVisitId)
        {
            return await _databaseService.DeleteFieldVisitAsync(fieldVisitId);
        }

        public async Task<bool> ClearAllFieldVisitsAsync()
        {
            return await _databaseService.ClearAllFieldVisitsAsync();
        }

        public async Task<FieldVisit?> GetFieldVisitByDriverContractAsync(string driverContract)
        {
            return await _databaseService.GetFieldVisitByDriverContractAsync(driverContract);
        }

        public async Task<bool> CheckVisitNumberExistsAsync(string visitNumber)
        {
            return await _databaseService.CheckVisitNumberExistsAsync(visitNumber);
        }

        // Projects methods
        public async Task<List<Project>> GetProjectsAsync()
        {
            return await _databaseService.GetProjectsAsync();
        }

        public async Task<Project?> GetProjectByNumberAsync(string projectNumber)
        {
            return await _databaseService.GetProjectByNumberAsync(projectNumber);
        }

        public async Task<bool> AddProjectAsync(Project project)
        {
            return await _databaseService.AddProjectAsync(project);
        }

        public async Task<bool> UpdateProjectAsync(Project project)
        {
            return await _databaseService.UpdateProjectAsync(project);
        }

        public async Task<bool> DeleteProjectAsync(int projectId)
        {
            return await _databaseService.DeleteProjectAsync(projectId);
        }

        // Driver Quotes methods
        public async Task<List<DriverQuote>> GetDriverQuotesAsync()
        {
            return await _databaseService.GetDriverQuotesAsync();
        }

        public async Task<List<DriverQuote>> GetDriverQuotesByStatusAsync(QuoteStatus status)
        {
            return await _databaseService.GetDriverQuotesByStatusAsync(status);
        }

        public async Task<bool> AddDriverQuoteAsync(DriverQuote quote)
        {
            return await _databaseService.AddDriverQuoteAsync(quote);
        }

        public async Task<bool> UpdateDriverQuoteAsync(DriverQuote quote)
        {
            return await _databaseService.UpdateDriverQuoteAsync(quote);
        }

        public async Task<bool> DeleteDriverQuoteAsync(int quoteId)
        {
            return await _databaseService.DeleteDriverQuoteAsync(quoteId);
        }

        public async Task<bool> UpdateDriverQuoteStatusAsync(int quoteId, QuoteStatus status)
        {
            return await _databaseService.UpdateDriverQuoteStatusAsync(quoteId, status);
        }

        public async Task<DriverQuote?> GetDriverQuoteByDriverIdAsync(int driverId)
        {
            return await _databaseService.GetDriverQuoteByDriverIdAsync(driverId);
        }

        // Refresh methods
        public async Task RefreshAllDataAsync()
        {
            await _databaseService.RefreshAllDataAsync();
        }

        // Offers methods
        public async Task<bool> SaveVisitOffersAsync(string visitNumber, string offersText, int daysCount)
        {
            return await _databaseService.SaveVisitOffersAsync(visitNumber, offersText, daysCount);
        }

        public async Task<bool> SaveWinnerDriverMessageAsync(string visitNumber, string messageText)
        {
            return await _databaseService.SaveWinnerDriverMessageAsync(visitNumber, messageText);
        }

        public async Task<bool> CleanAllDriverMessagesAsync()
        {
            return await _databaseService.CleanAllDriverMessagesAsync();
        }

        public async Task<List<DriverOffer>> GetVisitOffersAsync(string visitNumber)
        {
            return await _databaseService.GetVisitOffersAsync(visitNumber);
        }

        public async Task<bool> DeleteVisitOffersAsync(string visitNumber)
        {
            return await _databaseService.DeleteVisitOffersAsync(visitNumber);
        }

        // Report data methods
        public async Task<List<PriceOfferItem>> GetPriceOffersByVisitIdAsync(int visitId)
        {
            return await _databaseService.GetPriceOffersByVisitIdAsync(visitId);
        }

        public async Task<SelectedVehicleData> GetSelectedVehicleByVisitIdAsync(int visitId)
        {
            return await _databaseService.GetSelectedVehicleByVisitIdAsync(visitId);
        }

        // Field Visit Projects methods
        public async Task<(bool Success, List<string> Errors)> SaveFieldVisitProjectsAsync(int fieldVisitId, List<FieldVisitProject> projects)
        {
            return await _databaseService.SaveFieldVisitProjectsAsync(fieldVisitId, projects);
        }

        public async Task<List<FieldVisitProject>> GetFieldVisitProjectsAsync(int fieldVisitId)
        {
            return await _databaseService.GetFieldVisitProjectsAsync(fieldVisitId);
        }

        public async Task<List<FieldVisitProject>> GetProjectsByVisitIdAsync(int visitId)
        {
            return await _databaseService.GetProjectsByVisitIdAsync(visitId);
        }

        public async Task<List<FieldVisitor>> GetFieldVisitVisitorsAsync(int fieldVisitId)
        {
            return await _databaseService.GetFieldVisitVisitorsAsync(fieldVisitId);
        }



        public void Dispose()
        {
            _databaseService?.Dispose();
        }
    }
}
