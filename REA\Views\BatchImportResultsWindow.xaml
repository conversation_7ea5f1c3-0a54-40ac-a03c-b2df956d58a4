<Window x:Class="DriverManagementSystem.Views.BatchImportResultsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="📊 نتائج الاستيراد المتعدد" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Modern Button Style like Excel Import with Icons -->
        <Style x:Key="FormalButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}"
                               x:Name="border">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock x:Name="IconText" Text="{TemplateBinding Tag}"
                                          FontSize="16" FontWeight="Bold"
                                          Foreground="{TemplateBinding Foreground}"
                                          Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Padding="20" Margin="0,0,0,15">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667eea" Offset="0"/>
                    <GradientStop Color="#764ba2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel>
                <TextBlock Text="📊 نتائج الاستيراد المتعدد" 
                          FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="تقرير مفصل لجميع عمليات الاستيراد" 
                          FontSize="12" Foreground="#E8E8E8" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Summary Cards -->
        <Grid Grid.Row="1" Margin="20,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Files -->
            <Border Grid.Column="0" Background="White" CornerRadius="10" Padding="15" Margin="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📁" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalFiles}" FontSize="20" FontWeight="Bold" 
                              HorizontalAlignment="Center" Foreground="#2196F3"/>
                    <TextBlock Text="إجمالي الملفات" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                </StackPanel>
            </Border>

            <!-- Successful -->
            <Border Grid.Column="1" Background="White" CornerRadius="10" Padding="15" Margin="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding SuccessfulFiles}" FontSize="20" FontWeight="Bold" 
                              HorizontalAlignment="Center" Foreground="#4CAF50"/>
                    <TextBlock Text="نجح" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                </StackPanel>
            </Border>

            <!-- Failed -->
            <Border Grid.Column="2" Background="White" CornerRadius="10" Padding="15" Margin="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="❌" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding FailedFiles}" FontSize="20" FontWeight="Bold" 
                              HorizontalAlignment="Center" Foreground="#F44336"/>
                    <TextBlock Text="فشل" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                </StackPanel>
            </Border>

            <!-- Success Rate -->
            <Border Grid.Column="3" Background="White" CornerRadius="10" Padding="15" Margin="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📈" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding SuccessRate, StringFormat={}{0:F1}%}" FontSize="20" FontWeight="Bold" 
                              HorizontalAlignment="Center" Foreground="#9C27B0"/>
                    <TextBlock Text="معدل النجاح" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                </StackPanel>
            </Border>

            <!-- Processing Time -->
            <Border Grid.Column="4" Background="White" CornerRadius="10" Padding="15" Margin="5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="⏱️" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ProcessingTimeText}" FontSize="16" FontWeight="Bold" 
                              HorizontalAlignment="Center" Foreground="#FF9800"/>
                    <TextBlock Text="وقت المعالجة" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Results Table -->
        <Border Grid.Row="2" Background="White" CornerRadius="10" Margin="20" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="📋 تفاصيل النتائج" FontSize="16" FontWeight="Bold" 
                          Margin="0,0,0,15" Foreground="#333"/>

                <DataGrid Grid.Row="1" Name="ResultsDataGrid" 
                         ItemsSource="{Binding FileResults}"
                         AutoGenerateColumns="False" 
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         AlternatingRowBackground="#F8F9FA">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الملف" Binding="{Binding FileName}" Width="200"/>
                        <DataGridTextColumn Header="الرقم المرجعي" Binding="{Binding IndexValue}" Width="100"/>
                        <DataGridTextColumn Header="رقم الزيارة" Binding="{Binding VisitNumber}" Width="120"/>
                        <DataGridTextColumn Header="المشاريع" Binding="{Binding ProjectsCount}" Width="80"/>
                        <DataGridTextColumn Header="خط السير" Binding="{Binding ItineraryDaysCount}" Width="80"/>
                        
                        <DataGridTemplateColumn Header="الحالة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="15" Padding="8,4" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Success}" Value="True">
                                                        <Setter Property="Background" Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Success}" Value="False">
                                                        <Setter Property="Background" Value="#FFEBEE"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock HorizontalAlignment="Center" FontWeight="Bold">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Success}" Value="True">
                                                            <Setter Property="Text" Value="✅ نجح"/>
                                                            <Setter Property="Foreground" Value="#2E7D32"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Success}" Value="False">
                                                            <Setter Property="Text" Value="❌ فشل"/>
                                                            <Setter Property="Foreground" Value="#C62828"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="وقت المعالجة" Binding="{Binding ProcessingTime, StringFormat={}{0:F1}s}" Width="100"/>
                        <DataGridTextColumn Header="رسالة الخطأ" Binding="{Binding ErrorMessage}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Footer Buttons -->
        <Border Grid.Row="3" Background="#F5F5F5" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="تصدير النتائج" Tag="📤"
                        Background="#4CAF50" Foreground="White"
                        Padding="20,12" Margin="0,0,15,0"
                        Click="ExportResultsButton_Click"
                        Style="{StaticResource FormalButtonStyle}"/>

                <Button Content="إعادة المحاولة للفاشل" Tag="🔄"
                        Background="#FF9800" Foreground="White"
                        Padding="20,12" Margin="0,0,15,0"
                        Click="RetryFailedButton_Click"
                        Style="{StaticResource FormalButtonStyle}"/>

                <Button Content="إغلاق" Tag="✕"
                        Background="#F44336" Foreground="White"
                        Padding="20,12"
                        Click="CloseButton_Click"
                        Style="{StaticResource FormalButtonStyle}"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
