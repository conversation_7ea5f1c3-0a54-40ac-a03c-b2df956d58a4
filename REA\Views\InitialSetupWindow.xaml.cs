using System;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Input;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    public partial class InitialSetupWindow : Window
    {
        private readonly ApplicationDbContext _context;

        public InitialSetupWindow()
        {
            InitializeComponent();
            _context = new ApplicationDbContext();
            
            // تركيز على مربع كلمة المرور
            Loaded += (s, e) => PasswordBox.Focus();
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
                DragMove();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إغلاق النظام؟\nلن تتمكن من استخدام النظام بدون إنشاء حساب المدير.",
                "تأكيد الإغلاق",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                Application.Current.Shutdown();
            }
        }

        private async void CreateAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                CreateButton.IsEnabled = false;
                StatusMessage.Text = "⏳ جاري إنشاء الحساب...";

                // التحقق من صحة البيانات
                if (!ValidateInput())
                {
                    CreateButton.IsEnabled = true;
                    return;
                }

                // إنشاء المستخدم
                var user = new User
                {
                    Username = UsernameTextBox.Text.Trim(),
                    FullName = FullNameTextBox.Text.Trim(),
                    Email = EmailTextBox.Text.Trim(),
                    PasswordHash = HashPassword(PasswordBox.Password),
                    Role = "Admin",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = "System"
                };

                // حفظ في قاعدة البيانات
                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                // إنشاء الصلاحيات الافتراضية
                await CreateDefaultPermissions(user.UserId);

                StatusMessage.Text = "✅ تم إنشاء الحساب بنجاح!";
                StatusMessage.Foreground = System.Windows.Media.Brushes.LightGreen;

                // إظهار رسالة نجاح
                MessageBox.Show(
                    $"🎉 تم إنشاء حساب المدير بنجاح!\n\n" +
                    $"👤 اسم المستخدم: {user.Username}\n" +
                    $"📧 البريد الإلكتروني: {user.Email}\n\n" +
                    "سيتم فتح نافذة تسجيل الدخول الآن.",
                    "تم إنشاء الحساب بنجاح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                // إغلاق النافذة والانتقال لتسجيل الدخول
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                StatusMessage.Text = $"❌ خطأ: {ex.Message}";
                StatusMessage.Foreground = System.Windows.Media.Brushes.LightCoral;
                CreateButton.IsEnabled = true;

                MessageBox.Show(
                    $"حدث خطأ أثناء إنشاء الحساب:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            // التحقق من اسم المستخدم
            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
            {
                ShowError("يرجى إدخال اسم المستخدم");
                UsernameTextBox.Focus();
                return false;
            }

            if (UsernameTextBox.Text.Length < 3)
            {
                ShowError("اسم المستخدم يجب أن يكون 3 أحرف على الأقل");
                UsernameTextBox.Focus();
                return false;
            }

            // التحقق من الاسم الكامل
            if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
            {
                ShowError("يرجى إدخال الاسم الكامل");
                FullNameTextBox.Focus();
                return false;
            }

            // التحقق من البريد الإلكتروني
            if (string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                ShowError("يرجى إدخال البريد الإلكتروني");
                EmailTextBox.Focus();
                return false;
            }

            if (!IsValidEmail(EmailTextBox.Text))
            {
                ShowError("يرجى إدخال بريد إلكتروني صحيح");
                EmailTextBox.Focus();
                return false;
            }

            // التحقق من كلمة المرور
            if (string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                ShowError("يرجى إدخال كلمة المرور");
                PasswordBox.Focus();
                return false;
            }

            if (PasswordBox.Password.Length < 6)
            {
                ShowError("كلمة المرور يجب أن تكون 6 أحرف على الأقل");
                PasswordBox.Focus();
                return false;
            }

            // التحقق من تطابق كلمة المرور
            if (PasswordBox.Password != ConfirmPasswordBox.Password)
            {
                ShowError("كلمة المرور وتأكيد كلمة المرور غير متطابقتين");
                ConfirmPasswordBox.Focus();
                return false;
            }

            return true;
        }

        private void ShowError(string message)
        {
            StatusMessage.Text = $"❌ {message}";
            StatusMessage.Foreground = System.Windows.Media.Brushes.LightCoral;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                return emailRegex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }

        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        private async System.Threading.Tasks.Task CreateDefaultPermissions(int userId)
        {
            var permissions = new[]
            {
                new UserPermission { UserId = userId, PermissionName = "ViewDashboard", PermissionDescription = "عرض لوحة التحكم", IsGranted = true, CreatedBy = "System" },
                new UserPermission { UserId = userId, PermissionName = "ManageDrivers", PermissionDescription = "إدارة السائقين", IsGranted = true, CreatedBy = "System" },
                new UserPermission { UserId = userId, PermissionName = "ManageUsers", PermissionDescription = "إدارة المستخدمين", IsGranted = true, CreatedBy = "System" },
                new UserPermission { UserId = userId, PermissionName = "ViewReports", PermissionDescription = "عرض التقارير", IsGranted = true, CreatedBy = "System" },
                new UserPermission { UserId = userId, PermissionName = "ManageSettings", PermissionDescription = "إدارة الإعدادات", IsGranted = true, CreatedBy = "System" },
                new UserPermission { UserId = userId, PermissionName = "ManageContracts", PermissionDescription = "إدارة العقود", IsGranted = true, CreatedBy = "System" },
                new UserPermission { UserId = userId, PermissionName = "ManagePricing", PermissionDescription = "إدارة الأسعار", IsGranted = true, CreatedBy = "System" },
                new UserPermission { UserId = userId, PermissionName = "SendMessages", PermissionDescription = "إرسال الرسائل", IsGranted = true, CreatedBy = "System" },
                new UserPermission { UserId = userId, PermissionName = "ViewRoutes", PermissionDescription = "عرض الطرق", IsGranted = true, CreatedBy = "System" },
                new UserPermission { UserId = userId, PermissionName = "ManageDropData", PermissionDescription = "إدارة بيانات النزول", IsGranted = true, CreatedBy = "System" }
            };

            _context.UserPermissions.AddRange(permissions);
            await _context.SaveChangesAsync();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
