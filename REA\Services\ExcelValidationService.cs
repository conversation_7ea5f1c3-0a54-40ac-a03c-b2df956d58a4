using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة التحقق من صحة البيانات المستوردة من Excel
    /// </summary>
    public class ExcelValidationService
    {
        private readonly IDataService _dataService;

        public ExcelValidationService(IDataService dataService)
        {
            _dataService = dataService;
        }

        /// <summary>
        /// التحقق الشامل من صحة البيانات المستوردة
        /// </summary>
        public async Task<ValidationResult> ValidateImportDataAsync(FieldVisitImportResult importResult)
        {
            var result = new ValidationResult();
            
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 بدء التحقق من صحة البيانات المستوردة...");

                // التحقق من البيانات الأساسية
                await ValidateBasicDataAsync(importResult.VisitData, result);

                // التحقق من المشاريع
                await ValidateProjectsAsync(importResult.Projects, result);

                // التحقق من خط السير
                ValidateItinerary(importResult.Itinerary, result);

                // التحقق من التطابق والتناسق
                ValidateDataConsistency(importResult, result);

                // حساب درجة الجودة
                CalculateQualityScore(result);

                System.Diagnostics.Debug.WriteLine($"✅ انتهى التحقق - الأخطاء: {result.Errors.Count}, التحذيرات: {result.Warnings.Count}");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في عملية التحقق: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من البيانات: {ex.Message}");
            }

            return result;
        }

        private async Task ValidateBasicDataAsync(VisitImportData? visitData, ValidationResult result)
        {
            if (visitData == null)
            {
                result.Errors.Add("بيانات الزيارة مفقودة");
                return;
            }

            // التحقق من رقم الزيارة
            if (string.IsNullOrWhiteSpace(visitData.VisitFormNumber))
            {
                result.Errors.Add("رقم الزيارة مطلوب");
            }
            else if (visitData.VisitFormNumber.Length < 3)
            {
                result.Warnings.Add("رقم الزيارة قصير جداً");
            }

            // التحقق من عدد الأيام
            if (visitData.FieldDaysCount <= 0)
            {
                result.Errors.Add("عدد الأيام يجب أن يكون أكبر من صفر");
            }
            else if (visitData.FieldDaysCount > 30)
            {
                result.Warnings.Add("عدد الأيام كبير جداً (أكثر من 30 يوم)");
            }

            // التحقق من التواريخ
            if (visitData.StartDate.HasValue && visitData.EndDate.HasValue)
            {
                if (visitData.EndDate < visitData.StartDate)
                {
                    result.Errors.Add("تاريخ النهاية لا يمكن أن يكون قبل تاريخ البداية");
                }
                else
                {
                    var daysDifference = (visitData.EndDate.Value - visitData.StartDate.Value).Days + 1;
                    if (daysDifference != visitData.FieldDaysCount)
                    {
                        result.Warnings.Add($"عدد الأيام المحسوب ({daysDifference}) لا يطابق عدد الأيام المدخل ({visitData.FieldDaysCount})");
                    }
                }
            }

            // التحقق من القطاع
            if (!string.IsNullOrWhiteSpace(visitData.Sector))
            {
                var sector = await _dataService.GetSectorByCodeAsync(visitData.Sector);
                if (sector == null)
                {
                    result.Warnings.Add($"القطاع '{visitData.Sector}' غير موجود في النظام");
                }
            }

            // التحقق من مهمة النزول
            if (string.IsNullOrWhiteSpace(visitData.TripPurpose))
            {
                result.Warnings.Add("مهمة النزول فارغة");
            }
            else if (visitData.TripPurpose.Length < 10)
            {
                result.Warnings.Add("مهمة النزول قصيرة جداً");
            }
        }

        private async Task ValidateProjectsAsync(List<ProjectImportData> projects, ValidationResult result)
        {
            if (!projects.Any())
            {
                result.Warnings.Add("لا توجد مشاريع مستوردة");
                return;
            }

            var projectCodes = new HashSet<string>();
            var totalProjectDays = 0;

            foreach (var project in projects)
            {
                // التحقق من رقم المشروع
                if (string.IsNullOrWhiteSpace(project.ProjectCode))
                {
                    result.Errors.Add("رقم مشروع فارغ");
                    continue;
                }

                // التحقق من تكرار أرقام المشاريع
                if (!projectCodes.Add(project.ProjectCode))
                {
                    result.Errors.Add($"رقم المشروع '{project.ProjectCode}' مكرر");
                }

                // التحقق من اسم المشروع
                if (string.IsNullOrWhiteSpace(project.ProjectName))
                {
                    result.Warnings.Add($"اسم المشروع '{project.ProjectCode}' فارغ");
                }

                // التحقق من أيام المشروع
                if (project.ProjectDays <= 0)
                {
                    result.Errors.Add($"عدد أيام المشروع '{project.ProjectCode}' غير صحيح");
                }
                else
                {
                    totalProjectDays += project.ProjectDays;
                }

                // التحقق من وجود المشروع في النظام
                var existingProject = await _dataService.GetProjectByNumberAsync(project.ProjectCode);
                if (existingProject == null)
                {
                    result.Warnings.Add($"المشروع '{project.ProjectCode}' غير موجود في النظام");
                }
            }

            // التحقق من إجمالي أيام المشاريع
            result.TotalProjectDays = totalProjectDays;
        }

        private void ValidateItinerary(List<ItineraryDayImportData> itinerary, ValidationResult result)
        {
            if (!itinerary.Any())
            {
                result.Warnings.Add("خط السير فارغ");
                return;
            }

            for (int i = 0; i < itinerary.Count; i++)
            {
                var dayItem = itinerary[i];
                var dayPlan = dayItem.Plan;

                if (string.IsNullOrWhiteSpace(dayPlan))
                {
                    result.Warnings.Add($"خط السير لليوم {dayItem.DayNumber} فارغ");
                }
                else if (dayPlan.Length < 10)
                {
                    result.Warnings.Add($"خط السير لليوم {dayItem.DayNumber} قصير جداً");
                }
            }
        }

        private void ValidateDataConsistency(FieldVisitImportResult importResult, ValidationResult result)
        {
            if (importResult.VisitData == null) return;

            // التحقق من تطابق عدد الأيام مع خط السير
            if (importResult.Itinerary.Count > 0 && 
                importResult.Itinerary.Count != importResult.VisitData.FieldDaysCount)
            {
                result.Warnings.Add($"عدد أيام خط السير ({importResult.Itinerary.Count}) لا يطابق عدد أيام الزيارة ({importResult.VisitData.FieldDaysCount})");
            }

            // التحقق من تطابق إجمالي أيام المشاريع مع أيام الزيارة
            if (result.TotalProjectDays > 0 && 
                result.TotalProjectDays != importResult.VisitData.FieldDaysCount)
            {
                result.Warnings.Add($"إجمالي أيام المشاريع ({result.TotalProjectDays}) لا يطابق أيام الزيارة ({importResult.VisitData.FieldDaysCount})");
            }
        }

        private void CalculateQualityScore(ValidationResult result)
        {
            // حساب درجة الجودة من 0 إلى 100
            int baseScore = 100;
            
            // خصم نقاط للأخطاء
            baseScore -= result.Errors.Count * 20;
            
            // خصم نقاط للتحذيرات
            baseScore -= result.Warnings.Count * 5;
            
            result.QualityScore = Math.Max(0, baseScore);
        }
    }

    /// <summary>
    /// نتيجة التحقق من صحة البيانات
    /// </summary>
    public class ValidationResult
    {
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public int QualityScore { get; set; } = 100;
        public int TotalProjectDays { get; set; }
        
        public bool IsValid => !Errors.Any();
        public bool HasWarnings => Warnings.Any();
        
        public string GetQualityLevel()
        {
            return QualityScore switch
            {
                >= 90 => "ممتاز",
                >= 80 => "جيد جداً",
                >= 70 => "جيد",
                >= 60 => "مقبول",
                _ => "ضعيف"
            };
        }
    }
}
