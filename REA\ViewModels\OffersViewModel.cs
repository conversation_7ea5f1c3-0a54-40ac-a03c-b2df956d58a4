using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.ViewModels
{
    public class OffersViewModel : BindableBase
    {
        private readonly IDataService _dataService;
        private string _visitNumber = string.Empty;
        private int _visitDaysCount = 1;
        private string _winnerDriver = "لم يتم اختيار سائق بعد";
        private bool _hasWinner = false;
        private string _searchText = string.Empty;
        private bool _isLoading = false;
        private bool _isEditMode = false;

        public OffersViewModel(IDataService dataService)
        {
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            
            DriverOffers = new ObservableCollection<DriverOffer>();
            
            // تهيئة الأوامر
            FilterByPriceCommand = new DelegateCommand(ExecuteFilterByPrice, CanExecuteFilterByPrice);
            ApprovePriceCommand = new DelegateCommand(ExecuteApprovePrice, CanExecuteApprovePrice);
            ApproveWinnerCommand = new DelegateCommand(ExecuteApproveWinner, CanExecuteApproveWinner);
            SaveCommand = new DelegateCommand(ExecuteSave, CanExecuteSave);
            ClearSelectionCommand = new DelegateCommand(ExecuteClearSelection);
            RefreshCommand = new DelegateCommand(ExecuteRefresh);
            TransferSelectedDataCommand = new DelegateCommand(ExecuteTransferSelectedData, CanExecuteTransferSelectedData);
            
            // مراقبة تغييرات المجموعة
            DriverOffers.CollectionChanged += (s, e) => UpdateCommands();
        }

        #region Properties

        public ObservableCollection<DriverOffer> DriverOffers { get; }

        public string VisitNumber
        {
            get => _visitNumber;
            set
            {
                SetProperty(ref _visitNumber, value);
                UpdateWindowTitle();
            }
        }

        public int VisitDaysCount
        {
            get => _visitDaysCount;
            set
            {
                if (SetProperty(ref _visitDaysCount, value))
                {
                    UpdateAllDaysCount();
                }
            }
        }

        public string WinnerDriver
        {
            get => _winnerDriver;
            set => SetProperty(ref _winnerDriver, value);
        }

        public bool HasWinner
        {
            get => _hasWinner;
            set => SetProperty(ref _hasWinner, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    FilterOffers();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                SetProperty(ref _isEditMode, value);
                UpdateWindowTitle();
            }
        }

        public string WindowTitle => IsEditMode
            ? $"✏️ تعديل عروض الأسعار - الزيارة {VisitNumber}"
            : "🏆 نظام عروض الأسعار للسائقين";

        // إحصائيات
        public int TotalOffers => DriverOffers.Count;
        public int SelectedOffers => DriverOffers.Count(o => o.IsSelected);
        public decimal AveragePrice => DriverOffers.Any() ? DriverOffers.Average(o => o.ProposedAmount) : 0;
        public decimal LowestPrice => DriverOffers.Any() ? DriverOffers.Min(o => o.ProposedAmount) : 0;
        public decimal HighestPrice => DriverOffers.Any() ? DriverOffers.Max(o => o.ProposedAmount) : 0;

        #endregion

        #region Commands

        public DelegateCommand FilterByPriceCommand { get; }
        public DelegateCommand ApprovePriceCommand { get; }
        public DelegateCommand ApproveWinnerCommand { get; }
        public DelegateCommand SaveCommand { get; }
        public DelegateCommand ClearSelectionCommand { get; }
        public DelegateCommand RefreshCommand { get; }
        public DelegateCommand TransferSelectedDataCommand { get; }

        #endregion

        #region Command Implementations

        private void ExecuteFilterByPrice()
        {
            try
            {
                if (!DriverOffers.Any())
                {
                    ShowError("لا توجد عروض للفلترة");
                    return;
                }

                // ترتيب العروض حسب السعر (من الأقل إلى الأعلى)
                var sortedOffers = DriverOffers.OrderBy(o => o.ProposedAmount).ToList();
                DriverOffers.Clear();

                foreach (var offer in sortedOffers)
                {
                    DriverOffers.Add(offer);
                }

                // تحديد أقل الأسعار تلقائياً
                AutoSelectLowestPrices();

                UpdateStatistics();
                ShowSuccess($"تم ترتيب {sortedOffers.Count} عرض حسب السعر وتحديد أقل الأسعار تلقائياً");
                System.Diagnostics.Debug.WriteLine("✅ تم فرز العروض حسب السعر مع تحديد أقل الأسعار");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فرز العروض: {ex.Message}");
            }
        }

        private bool CanExecuteFilterByPrice()
        {
            return DriverOffers.Any() && !IsLoading;
        }

        /// <summary>
        /// تحديد أقل الأسعار تلقائياً
        /// </summary>
        private void AutoSelectLowestPrices()
        {
            try
            {
                // إلغاء جميع التحديدات أولاً
                foreach (var offer in DriverOffers)
                {
                    offer.IsSelected = false;
                    offer.IsWinner = false;
                    offer.OfferStatus = "تم التقديم";
                }

                // تحديد أقل 3 أسعار (أو جميع العروض إذا كانت أقل من 3)
                var lowestOffers = DriverOffers.OrderBy(o => o.ProposedAmount).Take(3).ToList();

                foreach (var offer in lowestOffers)
                {
                    offer.IsSelected = true;
                }

                // تحديد أقل سعر كفائز
                if (lowestOffers.Any())
                {
                    var winner = lowestOffers.First();
                    winner.IsWinner = true;
                    winner.OfferStatus = "🏆 فائز";
                }

                System.Diagnostics.Debug.WriteLine($"🎯 تم تحديد {lowestOffers.Count} من أقل الأسعار تلقائياً");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديد أقل الأسعار: {ex.Message}");
            }
        }

        private void ExecuteApproveWinner()
        {
            try
            {
                // البحث عن السائق الذي حالته "🏆 فائز"
                var winnerOffer = DriverOffers.FirstOrDefault(o => o.OfferStatus == "🏆 فائز");

                if (winnerOffer == null)
                {
                    ShowError("لا يوجد سائق بحالة 'فائز' ليتم اعتماده.\n\nيرجى تحديد سائق كفائز أولاً.");
                    return;
                }

                // تأكيد الاعتماد
                var confirmResult = MessageBox.Show(
                    $"هل تريد اعتماد السائق التالي كسائق معتمد للزيارة؟\n\n" +
                    $"🏆 السائق: {winnerOffer.DriverName}\n" +
                    $"💰 المبلغ: {winnerOffer.FormattedAmount}\n" +
                    $"📱 الهاتف: {winnerOffer.PhoneNumber}\n" +
                    $"🚗 المركبة: {winnerOffer.VehicleInfo}\n\n" +
                    $"سيتم حفظ هذا السائق كسائق معتمد للزيارة رقم {VisitNumber}",
                    "تأكيد اعتماد السائق الفائز",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (confirmResult == MessageBoxResult.Yes)
                {
                    // تحديث حالة السائق الفائز
                    winnerOffer.OfferStatus = "✅ معتمد";
                    winnerOffer.IsSelected = true;
                    winnerOffer.IsWinner = true;

                    // إلغاء اعتماد أي سائقين آخرين
                    foreach (var offer in DriverOffers.Where(o => o != winnerOffer))
                    {
                        if (offer.OfferStatus == "✅ معتمد")
                        {
                            offer.OfferStatus = "تم التقديم";
                        }
                        offer.IsWinner = false;
                    }

                    // تحديث معلومات الفائز
                    WinnerDriver = $"{winnerOffer.DriverName} - {winnerOffer.FormattedAmount}";
                    HasWinner = true;

                    UpdateStatistics();
                    UpdateCommands();

                    ShowSuccess($"✅ تم اعتماد السائق بنجاح!\n\n" +
                              $"🏆 السائق المعتمد: {winnerOffer.DriverName}\n" +
                              $"💰 المبلغ المعتمد: {winnerOffer.FormattedAmount}\n" +
                              $"📋 رقم الزيارة: {VisitNumber}\n\n" +
                              $"يمكنك الآن حفظ العروض لتأكيد الاعتماد في قاعدة البيانات.");

                    System.Diagnostics.Debug.WriteLine($"✅ تم اعتماد السائق الفائز: {winnerOffer.DriverName}");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في اعتماد السائق الفائز: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في اعتماد السائق الفائز: {ex.Message}");
            }
        }

        private bool CanExecuteApproveWinner()
        {
            return DriverOffers.Any(o => o.OfferStatus == "🏆 فائز") && !IsLoading;
        }

        private void ExecuteApprovePrice()
        {
            try
            {
                // البحث عن جميع العروض التي تحتوي على أسعار
                var validOffers = DriverOffers.Where(o => o.ProposedAmount > 0).ToList();

                if (validOffers.Any())
                {
                    // إلغاء تحديد جميع العروض أولاً
                    foreach (var offer in DriverOffers)
                    {
                        offer.IsWinner = false;
                        offer.IsSelected = false;
                    }

                    // تحديد جميع العروض التي تحتوي على أسعار
                    foreach (var offer in validOffers)
                    {
                        offer.IsSelected = true;
                    }

                    // البحث عن أقل عرض وتحديده كفائز
                    var lowestOffer = validOffers.OrderBy(o => o.ProposedAmount).First();
                    lowestOffer.IsWinner = true;

                    WinnerDriver = $"🏆 {lowestOffer.DriverName} - {lowestOffer.FormattedAmount}";
                    HasWinner = true;

                    // تحديث الإحصائيات والأوامر
                    UpdateStatistics();
                    UpdateCommands();

                    System.Diagnostics.Debug.WriteLine($"✅ تم اعتماد {validOffers.Count} عرض");
                    System.Diagnostics.Debug.WriteLine($"✅ تم اختيار الفائز: {lowestOffer.DriverName}");
                    System.Diagnostics.Debug.WriteLine($"✅ تم تفعيل زر الحفظ");
                    
                    // عرض رسالة نجاح للمستخدم
                    ShowSuccess($"تم اعتماد {validOffers.Count} عرض بنجاح\nتم اختيار {lowestOffer.DriverName} كأفضل عرض بسعر {lowestOffer.FormattedAmount}");
                }
                else
                {
                    ShowWarning("لا توجد عروض صالحة للاختيار");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في اختيار الفائز: {ex.Message}");
            }
        }

        private bool CanExecuteApprovePrice()
        {
            return DriverOffers.Any(o => o.ProposedAmount > 0) && !IsLoading;
        }

        private async void ExecuteSave()
        {
            try
            {
                IsLoading = true;

                var selectedOffers = DriverOffers.Where(o => o.IsSelected).ToList();

                if (!selectedOffers.Any())
                {
                    ShowWarning("يرجى اختيار عرض واحد على الأقل للحفظ");
                    return;
                }

                // ترتيب العروض المختارة حسب السعر (من الأقل إلى الأعلى) للحفاظ على الترتيب
                var sortedSelectedOffers = selectedOffers.OrderBy(o => o.ProposedAmount).ToList();

                // تحويل العروض إلى نص للحفظ مع الحفاظ على الترتيب
                var offersText = string.Join(" | ", sortedSelectedOffers.Select(o => o.ToSaveString()));

                // حفظ في قاعدة البيانات
                var success = await SaveOffersToDatabase(offersText);

                if (success)
                {
                    var successMessage = IsEditMode
                        ? $"✅ تم تحديث العروض بنجاح!\n\n📊 عدد العروض المحدثة: {sortedSelectedOffers.Count}\n⏰ وقت التحديث: {DateTime.Now:yyyy/MM/dd HH:mm:ss}\n\n🎯 العروض مرتبة حسب السعر (الأقل أولاً):\n{offersText}"
                        : $"✅ تم حفظ العروض بنجاح!\n\n📊 عدد العروض المحفوظة: {sortedSelectedOffers.Count}\n⏰ وقت الحفظ: {DateTime.Now:yyyy/MM/dd HH:mm:ss}\n\n🎯 العروض مرتبة حسب السعر (الأقل أولاً):\n{offersText}";

                    ShowSuccess(successMessage);

                    // إغلاق النافذة مع إرجاع true للإشارة إلى نجاح الحفظ
                    CloseWindow(true);

                    System.Diagnostics.Debug.WriteLine($"💾 تم حفظ العروض مرتبة حسب السعر: {string.Join(", ", sortedSelectedOffers.Select(o => $"{o.DriverName}({o.ProposedAmount})"))}");
                }
                else
                {
                    // الحصول على رسالة خطأ مفصلة
                    var detailedError = await GetDetailedSaveError();
                    var errorMessage = IsEditMode ?
                        $"❌ فشل في تحديث العروض\n\n🔍 السبب المحتمل:\n{detailedError}" :
                        $"❌ فشل في حفظ العروض\n\n🔍 السبب المحتمل:\n{detailedError}";
                    ShowError(errorMessage);
                }
            }
            catch (Exception ex)
            {
                var detailedError = $"❌ خطأ غير متوقع في حفظ العروض\n\n" +
                                  $"🔍 تفاصيل الخطأ:\n{ex.Message}\n\n" +
                                  $"📋 معلومات إضافية:\n" +
                                  $"• رقم الزيارة: {VisitNumber ?? "غير محدد"}\n" +
                                  $"• عدد العروض المحددة: {DriverOffers?.Count(o => o.IsSelected) ?? 0}\n" +
                                  $"• عدد أيام الزيارة: {VisitDaysCount}\n\n" +
                                  $"💡 يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني";
                ShowError(detailedError);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// إغلاق النافذة مع تحديد نتيجة الحوار
        /// </summary>
        /// <param name="dialogResult">نتيجة الحوار</param>
        private void CloseWindow(bool dialogResult)
        {
            try
            {
                // البحث عن النافذة وإغلاقها
                foreach (Window window in Application.Current.Windows)
                {
                    if (window.DataContext == this)
                    {
                        try
                        {
                            window.DialogResult = dialogResult;
                        }
                        catch
                        {
                            // إذا فشل تحديد DialogResult، أغلق النافذة فقط
                        }
                        window.Close();
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إغلاق النافذة: {ex.Message}");
            }
        }

        private bool CanExecuteSave()
        {
            return DriverOffers.Any(o => o.IsSelected) && !IsLoading;
        }

        private void ExecuteClearSelection()
        {
            foreach (var offer in DriverOffers)
            {
                offer.IsSelected = false;
                offer.IsWinner = false;
            }

            WinnerDriver = "لم يتم اختيار سائق بعد";
            HasWinner = false;
            UpdateStatistics();
        }

        private async void ExecuteRefresh()
        {
            try
            {
                IsLoading = true;
                await LoadDriverOffers();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحديث البيانات: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ExecuteTransferSelectedData()
        {
            try
            {
                var selectedOffers = DriverOffers.Where(o => o.IsSelected).ToList();
                
                if (!selectedOffers.Any())
                {
                    ShowWarning("يرجى اختيار سائق واحد على الأقل لنقل الأسعار");
                    return;
                }
                
                // إنشاء نص يحتوي على أسعار السائقين المختارين فقط
                var pricesText = "";
                
                foreach (var offer in selectedOffers)
                {
                    pricesText += $"{offer.DriverName}: {offer.ProposedAmount} ريال\n";
                }
                
                // نسخ الأسعار إلى الحافظة
                try
                {
                    System.Windows.Clipboard.SetText(pricesText);
                    ShowSuccess("تم نسخ أسعار السائقين المختارين إلى الحافظة بنجاح");
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في نسخ الأسعار: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في نقل الأسعار: {ex.Message}");
            }
        }
        
        private bool CanExecuteTransferSelectedData()
        {
            return DriverOffers.Any(o => o.IsSelected) && !IsLoading;
        }
        
        #endregion

        #region Public Methods

        /// <summary>
        /// تحميل عروض السائقين من قائمة السائقين المحددين
        /// </summary>
        public async Task LoadDriverOffers(ObservableCollection<Driver> selectedDrivers = null)
        {
            try
            {
                IsLoading = true;
                DriverOffers.Clear();

                if (selectedDrivers?.Any() == true)
                {
                    foreach (var driver in selectedDrivers)
                    {
                        var offer = DriverOffer.FromDriver(driver, VisitDaysCount);

                        // إضافة مستمع لتغييرات الخصائص
                        offer.PropertyChanged += (s, e) =>
                        {
                            if (e.PropertyName == nameof(DriverOffer.IsSelected))
                            {
                                UpdateCommands();
                                UpdateStatistics();
                            }
                        };

                        DriverOffers.Add(offer);
                    }
                }
                else
                {
                    // تحميل جميع السائقين النشطين
                    var allDrivers = await _dataService.GetDriversAsync();
                    var activeDrivers = allDrivers.Where(d => d.IsActive).ToList();

                    foreach (var driver in activeDrivers)
                    {
                        var offer = DriverOffer.FromDriver(driver, VisitDaysCount);

                        // إضافة مستمع لتغييرات الخصائص
                        offer.PropertyChanged += (s, e) =>
                        {
                            if (e.PropertyName == nameof(DriverOffer.IsSelected))
                            {
                                UpdateCommands();
                                UpdateStatistics();
                            }
                        };

                        DriverOffers.Add(offer);
                    }
                }

                UpdateStatistics();
                UpdateCommands();

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {DriverOffers.Count} عرض");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل العروض: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// تحديث عدد الأيام لجميع العروض
        /// </summary>
        public void UpdateAllDaysCount()
        {
            foreach (var offer in DriverOffers)
            {
                offer.DaysCount = VisitDaysCount;
            }
            UpdateStatistics();
        }

        /// <summary>
        /// تحميل العروض المحفوظة للتعديل - نسخة محسنة احترافية
        /// </summary>
        /// <param name="visitNumber">رقم الزيارة</param>
        public async Task LoadSavedOffers(string visitNumber)
        {
            try
            {
                IsLoading = true;
                DriverOffers.Clear();

                System.Diagnostics.Debug.WriteLine($"🔍 بدء تحميل العروض المحفوظة للزيارة: {visitNumber}");

                // التحقق من صحة رقم الزيارة
                if (string.IsNullOrWhiteSpace(visitNumber))
                {
                    ShowError("رقم الزيارة غير صحيح");
                    return;
                }

                // إنشاء خدمة العروض مع ApplicationDbContext جديد
                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new OffersService(context);

                // جلب العروض المحفوظة مع حالاتها من قاعدة البيانات
                var savedOffers = await offersService.GetSavedOffersAsync(visitNumber);

                if (savedOffers?.Any() == true)
                {
                    var loadedCount = 0;
                    var selectedCount = 0;

                    foreach (var offer in savedOffers)
                    {
                        // التحقق من صحة بيانات العرض
                        if (ValidateOfferData(offer))
                        {
                            // إضافة مستمع لتغييرات الخصائص
                            offer.PropertyChanged += (s, e) =>
                            {
                                if (e.PropertyName == nameof(DriverOffer.IsSelected))
                                {
                                    UpdateCommands();
                                    UpdateStatistics();
                                }
                            };

                            DriverOffers.Add(offer);
                            loadedCount++;

                            if (offer.IsSelected)
                                selectedCount++;

                            System.Diagnostics.Debug.WriteLine($"✅ تم تحميل عرض محفوظ: {offer.DriverName} - {offer.FormattedAmount} - مختار: {offer.IsSelected}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ تم تجاهل عرض غير صحيح: {offer?.DriverName ?? "غير معروف"}");
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {loadedCount} عرض محفوظ للتعديل ({selectedCount} مختار)");

                    // إظهار رسالة نجاح إذا تم تحميل العروض
                    if (loadedCount > 0)
                    {
                        ShowSuccess($"تم تحميل {loadedCount} عرض محفوظ بنجاح" +
                                  (selectedCount > 0 ? $" ({selectedCount} مختار)" : ""));
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على عروض محفوظة للزيارة {visitNumber}");

                    // في حالة عدم وجود عروض محفوظة، تحميل جميع السائقين النشطين
                    ShowWarning($"لا توجد عروض محفوظة للزيارة {visitNumber}. سيتم تحميل جميع السائقين النشطين.");
                    await LoadDriverOffers(null);
                }

                UpdateStatistics();
                UpdateCommands();
            }
            catch (Exception ex)
            {
                var errorMessage = $"خطأ في تحميل العروض المحفوظة للزيارة {visitNumber}:\n{ex.Message}";
                ShowError(errorMessage);
                System.Diagnostics.Debug.WriteLine($"❌ {errorMessage}");

                // في حالة الخطأ، محاولة تحميل السائقين النشطين كبديل
                try
                {
                    await LoadDriverOffers(null);
                    ShowWarning("تم تحميل السائقين النشطين كبديل بسبب خطأ في تحميل العروض المحفوظة.");
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحميل البديل: {fallbackEx.Message}");
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات العرض
        /// </summary>
        private bool ValidateOfferData(DriverOffer offer)
        {
            return offer != null &&
                   !string.IsNullOrWhiteSpace(offer.DriverName) &&
                   offer.ProposedAmount > 0 &&
                   offer.DaysCount > 0;
        }

        /// <summary>
        /// تحديث عنوان النافذة
        /// </summary>
        private void UpdateWindowTitle()
        {
            RaisePropertyChanged(nameof(WindowTitle));
        }

        #endregion

        #region Private Methods

        private void FilterOffers()
        {
            // تطبيق فلتر البحث (يمكن تطويره لاحقاً)
            UpdateStatistics();
        }

        private void UpdateStatistics()
        {
            RaisePropertyChanged(nameof(TotalOffers));
            RaisePropertyChanged(nameof(SelectedOffers));
            RaisePropertyChanged(nameof(AveragePrice));
            RaisePropertyChanged(nameof(LowestPrice));
            RaisePropertyChanged(nameof(HighestPrice));
        }

        private void UpdateCommands()
        {
            FilterByPriceCommand.RaiseCanExecuteChanged();
            ApprovePriceCommand.RaiseCanExecuteChanged();
            ApproveWinnerCommand.RaiseCanExecuteChanged();
            SaveCommand.RaiseCanExecuteChanged();
            TransferSelectedDataCommand.RaiseCanExecuteChanged();
        }

        private async Task<bool> SaveOffersToDatabase(string offersText)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"💾 بدء حفظ العروض للزيارة: {VisitNumber}");

                // إنشاء خدمة العروض مع ApplicationDbContext جديد
                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new OffersService(context);

                // حذف العروض القديمة أولاً
                await offersService.DeleteVisitOffersAsync(VisitNumber);

                // حفظ العروض الجديدة - تضمين جميع العروض التي لها حالة محددة (حتى المعتذرين)
                var selectedOffers = DriverOffers.Where(o => o.IsSelected || !string.IsNullOrEmpty(o.OfferStatus)).ToList();
                var success = await offersService.SaveVisitOffersAsync(selectedOffers, VisitNumber, VisitDaysCount);

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ {selectedOffers.Count} عرض للزيارة {VisitNumber}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في حفظ العروض للزيارة {VisitNumber}");
                    // حفظ تفاصيل الخطأ للاستخدام في رسالة الخطأ
                    _lastSaveError = await DiagnoseSaveError(selectedOffers);
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ العروض: {ex.Message}");
                _lastSaveError = $"خطأ في الاتصال بقاعدة البيانات:\n{ex.Message}";
                return false;
            }
        }

        private string _lastSaveError = "";

        /// <summary>
        /// تشخيص سبب فشل الحفظ
        /// </summary>
        private async Task<string> DiagnoseSaveError(List<DriverOffer> selectedOffers)
        {
            try
            {
                var errors = new List<string>();

                // فحص البيانات الأساسية
                if (string.IsNullOrWhiteSpace(VisitNumber))
                {
                    errors.Add("• رقم الزيارة غير محدد أو فارغ");
                }

                if (selectedOffers?.Any() != true)
                {
                    errors.Add("• لا توجد عروض محددة للحفظ");
                }

                if (VisitDaysCount <= 0)
                {
                    errors.Add("• عدد أيام الزيارة غير صحيح");
                }

                // فحص بيانات العروض
                if (selectedOffers?.Any() == true)
                {
                    for (int i = 0; i < selectedOffers.Count; i++)
                    {
                        var offer = selectedOffers[i];
                        if (string.IsNullOrWhiteSpace(offer.DriverName))
                        {
                            errors.Add($"• العرض رقم {i + 1}: اسم السائق فارغ");
                        }
                        if (offer.ProposedAmount <= 0)
                        {
                            errors.Add($"• العرض رقم {i + 1}: المبلغ المقترح غير صحيح ({offer.ProposedAmount})");
                        }
                    }
                }

                // فحص الاتصال بقاعدة البيانات
                try
                {
                    using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                    await context.Database.CanConnectAsync();
                }
                catch (Exception dbEx)
                {
                    errors.Add($"• مشكلة في الاتصال بقاعدة البيانات: {dbEx.Message}");
                }

                // فحص وجود العمود VisitNumber
                try
                {
                    using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                    var testQuery = context.DriverQuotes.Where(q => q.VisitNumber != null).Take(1);
                    await testQuery.ToListAsync();
                }
                catch (Exception colEx)
                {
                    errors.Add($"• مشكلة في هيكل قاعدة البيانات (العمود VisitNumber): {colEx.Message}");
                }

                if (errors.Any())
                {
                    return string.Join("\n", errors);
                }
                else
                {
                    return "• سبب غير معروف - يرجى المحاولة مرة أخرى";
                }
            }
            catch (Exception ex)
            {
                return $"• خطأ في تشخيص المشكلة: {ex.Message}";
            }
        }

        /// <summary>
        /// الحصول على رسالة الخطأ المفصلة
        /// </summary>
        private async Task<string> GetDetailedSaveError()
        {
            if (!string.IsNullOrEmpty(_lastSaveError))
            {
                return _lastSaveError;
            }

            // إذا لم يكن هناك خطأ محفوظ، قم بتشخيص سريع
            var selectedOffers = DriverOffers?.Where(o => o.IsSelected).ToList();
            return await DiagnoseSaveError(selectedOffers ?? new List<DriverOffer>());
        }

        private void ShowSuccess(string message)
        {
            MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ShowError(string message)
        {
            MessageBox.Show(message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private void ShowWarning(string message)
        {
            MessageBox.Show(message, "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        #endregion
    }
}
