using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace DriverManagementSystem.Data
{
    /// <summary>
    /// إعدادات قاعدة البيانات - SQL Server مع حفظ في مجلد Data
    /// </summary>
    public static class DatabaseConfig
    {
        private static readonly string ConfigFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "db_config.txt");

        /// <summary>
        /// الحصول على connection string لـ SQL Server
        /// </summary>
        public static string GetConnectionString()
        {
            var settings = LoadSettings();

            // إذا كانت قاعدة البيانات محلية
            if (settings.UseLocalDatabase)
            {
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                var dbPath = Path.Combine(dataFolder, $"{settings.DatabaseName}.mdf");

                // إنشاء مجلد Data إذا لم يكن موجوداً
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                return $@"Server=(LocalDB)\MSSQLLocalDB;AttachDbFilename={dbPath};Database={settings.DatabaseName};Trusted_Connection=True;MultipleActiveResultSets=True;Connection Timeout=30;";
            }

            // قاعدة بيانات خادم عادية
            if (settings.UseWindowsAuth)
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};Trusted_Connection=true;TrustServerCertificate=true;";
            }
            else
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};User Id={settings.Username};Password={settings.Password};TrustServerCertificate=true;";
            }
        }

        /// <summary>
        /// فئة إعدادات قاعدة البيانات
        /// </summary>
        public class DatabaseSettings
        {
            public string ServerName { get; set; } = "localhost";
            public string DatabaseName { get; set; } = "SFDSYS";
            public bool UseWindowsAuth { get; set; } = true;
            public string Username { get; set; } = "sa";
            public string Password { get; set; } = "";
            public bool UseLocalDatabase { get; set; } = false; // قاعدة بيانات محلية في مجلد Data
            public string LocalDatabasePath { get; set; } = ""; // مسار ملف قاعدة البيانات المحلية
        }

        /// <summary>
        /// تحميل الإعدادات من الملف
        /// </summary>
        public static DatabaseSettings LoadSettings()
        {
            try
            {
                // إنشاء مجلد Data إذا لم يكن موجوداً
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                if (File.Exists(ConfigFilePath))
                {
                    var lines = File.ReadAllLines(ConfigFilePath);
                    var settings = new DatabaseSettings();

                    foreach (var line in lines)
                    {
                        var parts = line.Split('=');
                        if (parts.Length == 2)
                        {
                            var key = parts[0].Trim();
                            var value = parts[1].Trim();

                            switch (key)
                            {
                                case "ServerName":
                                    settings.ServerName = value;
                                    break;
                                case "DatabaseName":
                                    settings.DatabaseName = value;
                                    break;
                                case "UseWindowsAuth":
                                    settings.UseWindowsAuth = bool.Parse(value);
                                    break;
                                case "Username":
                                    settings.Username = value;
                                    break;
                                case "Password":
                                    settings.Password = value;
                                    break;
                                case "UseLocalDatabase":
                                    settings.UseLocalDatabase = bool.Parse(value);
                                    break;
                                case "LocalDatabasePath":
                                    settings.LocalDatabasePath = value;
                                    break;
                            }
                        }
                    }

                    return settings;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل إعدادات قاعدة البيانات: {ex.Message}");
            }

            return new DatabaseSettings();
        }

        /// <summary>
        /// حفظ الإعدادات في الملف
        /// </summary>
        public static void SaveSettings(DatabaseSettings settings)
        {
            try
            {
                // إنشاء مجلد Data إذا لم يكن موجوداً
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                var lines = new[]
                {
                    $"ServerName={settings.ServerName}",
                    $"DatabaseName={settings.DatabaseName}",
                    $"UseWindowsAuth={settings.UseWindowsAuth}",
                    $"Username={settings.Username}",
                    $"Password={settings.Password}",
                    $"UseLocalDatabase={settings.UseLocalDatabase}",
                    $"LocalDatabasePath={settings.LocalDatabasePath}"
                };

                File.WriteAllLines(ConfigFilePath, lines);
                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ إعدادات قاعدة البيانات في: {ConfigFilePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ إعدادات قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود ملف الإعدادات
        /// </summary>
        public static bool HasConfigFile()
        {
            return File.Exists(ConfigFilePath);
        }

        /// <summary>
        /// بناء connection string من الإعدادات
        /// </summary>
        public static string BuildConnectionString(DatabaseSettings settings)
        {
            // إذا كانت قاعدة البيانات محلية
            if (settings.UseLocalDatabase)
            {
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                var dbPath = Path.Combine(dataFolder, $"{settings.DatabaseName}.mdf");

                // التأكد من وجود مجلد Data
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                // استخدام connection string محسن للـ LocalDB
                return $@"Server=(LocalDB)\MSSQLLocalDB;AttachDbFilename={dbPath};Database={settings.DatabaseName};Trusted_Connection=True;MultipleActiveResultSets=True;";
            }

            // قاعدة بيانات خادم عادية
            if (settings.UseWindowsAuth)
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};Trusted_Connection=true;TrustServerCertificate=true;MultipleActiveResultSets=True;";
            }
            else
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};User Id={settings.Username};Password={settings.Password};TrustServerCertificate=true;MultipleActiveResultSets=True;";
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        public static async Task<bool> TestConnectionAsync(string connectionString)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 اختبار الاتصال: {connectionString.Replace("Password=", "Password=***")}");

                using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                await connection.OpenAsync();

                System.Diagnostics.Debug.WriteLine("✅ نجح اختبار الاتصال");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل اختبار الاتصال: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء قاعدة البيانات إذا لم تكن موجودة
        /// </summary>
        public static async Task<bool> CreateDatabaseIfNotExists()
        {
            try
            {
                var settings = LoadSettings();

                // إذا كانت قاعدة البيانات محلية
                if (settings.UseLocalDatabase)
                {
                    return await CreateLocalDatabaseIfNotExists(settings);
                }

                // التحقق من وجود قاعدة البيانات أولاً
                if (await DatabaseExists(settings))
                {
                    System.Diagnostics.Debug.WriteLine($"✅ قاعدة البيانات موجودة بالفعل: {settings.DatabaseName}");
                    return true;
                }

                // قاعدة بيانات خادم عادية
                var masterConnectionString = BuildConnectionString(new DatabaseSettings
                {
                    ServerName = settings.ServerName,
                    DatabaseName = "master",
                    UseWindowsAuth = settings.UseWindowsAuth,
                    Username = settings.Username,
                    Password = settings.Password,
                    UseLocalDatabase = false
                });

                using var connection = new Microsoft.Data.SqlClient.SqlConnection(masterConnectionString);
                await connection.OpenAsync();

                var createDbCommand = new Microsoft.Data.SqlClient.SqlCommand(
                    $"CREATE DATABASE [{settings.DatabaseName}]", connection);
                await createDbCommand.ExecuteNonQueryAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء قاعدة البيانات: {settings.DatabaseName}");

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود قاعدة البيانات
        /// </summary>
        public static async Task<bool> DatabaseExists(DatabaseSettings settings = null)
        {
            try
            {
                if (settings == null)
                    settings = LoadSettings();

                // إذا كانت قاعدة البيانات محلية
                if (settings.UseLocalDatabase)
                {
                    var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                    var dbPath = Path.Combine(dataFolder, $"{settings.DatabaseName}.mdf");
                    return File.Exists(dbPath);
                }

                // قاعدة بيانات خادم عادية
                var masterConnectionString = BuildConnectionString(new DatabaseSettings
                {
                    ServerName = settings.ServerName,
                    DatabaseName = "master",
                    UseWindowsAuth = settings.UseWindowsAuth,
                    Username = settings.Username,
                    Password = settings.Password,
                    UseLocalDatabase = false
                });

                using var connection = new Microsoft.Data.SqlClient.SqlConnection(masterConnectionString);
                await connection.OpenAsync();

                var checkDbCommand = new Microsoft.Data.SqlClient.SqlCommand(
                    $"SELECT COUNT(*) FROM sys.databases WHERE name = '{settings.DatabaseName}'", connection);
                var dbExists = (int)await checkDbCommand.ExecuteScalarAsync() > 0;

                return dbExists;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص وجود قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء قاعدة البيانات المحلية
        /// </summary>
        public static async Task<bool> CreateLocalDatabaseIfNotExists(DatabaseSettings settings)
        {
            try
            {
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                var dbPath = Path.Combine(dataFolder, $"{settings.DatabaseName}.mdf");
                var logPath = Path.Combine(dataFolder, $"{settings.DatabaseName}_Log.ldf");

                // إنشاء مجلد Data إذا لم يكن موجوداً
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                // إذا كان ملف قاعدة البيانات موجود، لا نحتاج لإنشائه
                if (File.Exists(dbPath))
                {
                    System.Diagnostics.Debug.WriteLine($"✅ قاعدة البيانات المحلية موجودة: {dbPath}");
                    return true;
                }

                // التحقق من وجود قاعدة البيانات في LocalDB أولاً
                var masterConnectionString = @"Server=(LocalDB)\MSSQLLocalDB;Database=master;Trusted_Connection=True;";

                using var connection = new Microsoft.Data.SqlClient.SqlConnection(masterConnectionString);
                await connection.OpenAsync();

                // فحص وجود قاعدة البيانات في LocalDB
                var checkDbCommand = new Microsoft.Data.SqlClient.SqlCommand(
                    $"SELECT COUNT(*) FROM sys.databases WHERE name = '{settings.DatabaseName}'", connection);
                var dbExists = (int)await checkDbCommand.ExecuteScalarAsync() > 0;

                if (dbExists)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ قاعدة البيانات موجودة في LocalDB: {settings.DatabaseName}");
                    return true;
                }

                // إنشاء قاعدة البيانات المحلية
                var createDbCommand = new Microsoft.Data.SqlClient.SqlCommand($@"
                    CREATE DATABASE [{settings.DatabaseName}]
                    ON (NAME = '{settings.DatabaseName}', FILENAME = '{dbPath}')
                    LOG ON (NAME = '{settings.DatabaseName}_Log', FILENAME = '{logPath}')
                ", connection);

                await createDbCommand.ExecuteNonQueryAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء قاعدة البيانات المحلية: {dbPath}");

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء قاعدة البيانات المحلية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من إمكانية الاتصال بقاعدة البيانات
        /// </summary>
        public static async Task<bool> CanConnectToDatabase()
        {
            try
            {
                var connectionString = GetConnectionString();
                System.Diagnostics.Debug.WriteLine($"🔍 محاولة الاتصال بقاعدة البيانات...");
                System.Diagnostics.Debug.WriteLine($"   Connection String: {connectionString.Replace("Password=", "Password=***")}");

                using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                await connection.OpenAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم الاتصال بقاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ لا يمكن الاتصال بقاعدة البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"   نوع الخطأ: {ex.GetType().Name}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"   الخطأ الداخلي: {ex.InnerException.Message}");
                }
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود LocalDB
        /// </summary>
        public static async Task<bool> IsLocalDBAvailable()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 فحص توفر LocalDB...");

                var testConnectionString = @"Server=(LocalDB)\MSSQLLocalDB;Database=master;Trusted_Connection=True;Connection Timeout=10;";
                using var connection = new Microsoft.Data.SqlClient.SqlConnection(testConnectionString);
                await connection.OpenAsync();
                System.Diagnostics.Debug.WriteLine("✅ LocalDB متوفر ويعمل");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ LocalDB غير متوفر أو لا يعمل: {ex.Message}");

                // محاولة تشغيل LocalDB
                var started = await TryStartLocalDB();
                if (started)
                {
                    // إعادة المحاولة بعد تشغيل LocalDB
                    try
                    {
                        var testConnectionString2 = @"Server=(LocalDB)\MSSQLLocalDB;Database=master;Trusted_Connection=True;Connection Timeout=10;";
                        using var connection2 = new Microsoft.Data.SqlClient.SqlConnection(testConnectionString2);
                        await connection2.OpenAsync();
                        System.Diagnostics.Debug.WriteLine("✅ LocalDB يعمل الآن بعد التشغيل");
                        return true;
                    }
                    catch (Exception ex2)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ LocalDB لا يزال لا يعمل بعد التشغيل: {ex2.Message}");
                        return false;
                    }
                }

                return false;
            }
        }

        /// <summary>
        /// محاولة تشغيل LocalDB
        /// </summary>
        public static async Task<bool> TryStartLocalDB()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 محاولة تشغيل LocalDB...");

                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sqllocaldb",
                    Arguments = "start MSSQLLocalDB",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();

                    System.Diagnostics.Debug.WriteLine($"LocalDB Start Output: {output}");
                    if (!string.IsNullOrEmpty(error))
                    {
                        System.Diagnostics.Debug.WriteLine($"LocalDB Start Error: {error}");
                    }

                    if (process.ExitCode == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("✅ تم تشغيل LocalDB بنجاح");
                        return true;
                    }
                }

                System.Diagnostics.Debug.WriteLine("❌ فشل في تشغيل LocalDB");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تشغيل LocalDB: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على قائمة قواعد البيانات المتاحة في LocalDB
        /// </summary>
        public static async Task<List<string>> GetAvailableDatabases()
        {
            var databases = new List<string>();
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 جلب قائمة قواعد البيانات...");

                var connectionString = @"Server=(LocalDB)\MSSQLLocalDB;Database=master;Trusted_Connection=True;Connection Timeout=10;";
                using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                await connection.OpenAsync();

                var query = @"SELECT name FROM sys.databases
                             WHERE name NOT IN ('master', 'tempdb', 'model', 'msdb')
                             ORDER BY name";

                using var command = new Microsoft.Data.SqlClient.SqlCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    databases.Add(reader.GetString(0));
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {databases.Count} قاعدة بيانات");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب قواعد البيانات: {ex.Message}");
            }

            return databases;
        }

        /// <summary>
        /// حذف قاعدة بيانات من LocalDB
        /// </summary>
        public static async Task<bool> DeleteDatabase(string databaseName)
        {
            // محاولة الطريقة الأولى: SQL Commands
            var sqlResult = await DeleteDatabaseWithSQL(databaseName);
            if (sqlResult)
                return true;

            // محاولة الطريقة الثانية: SqlLocalDB Command Line
            var cmdResult = await DeleteDatabaseWithCommand(databaseName);
            if (cmdResult)
                return true;

            // محاولة الطريقة الثالثة: حذف ملفات قاعدة البيانات مباشرة
            var fileResult = await DeleteDatabaseFiles(databaseName);
            return fileResult;
        }

        /// <summary>
        /// حذف قاعدة البيانات باستخدام SQL Commands
        /// </summary>
        private static async Task<bool> DeleteDatabaseWithSQL(string databaseName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🗑️ محاولة حذف قاعدة البيانات بـ SQL: {databaseName}");

                var connectionString = @"Server=(LocalDB)\MSSQLLocalDB;Database=master;Trusted_Connection=True;Connection Timeout=30;";
                using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                await connection.OpenAsync();

                // الخطوة 1: إنهاء جميع الاتصالات النشطة
                var killConnectionsQuery = $@"
                    DECLARE @sql NVARCHAR(500) = ''
                    SELECT @sql = @sql + 'KILL ' + CONVERT(VARCHAR(5), session_id) + '; '
                    FROM sys.dm_exec_sessions
                    WHERE database_id = DB_ID('{databaseName}')
                    AND session_id != @@SPID

                    IF LEN(@sql) > 0 EXEC(@sql)";

                try
                {
                    using var killCommand = new Microsoft.Data.SqlClient.SqlCommand(killConnectionsQuery, connection);
                    await killCommand.ExecuteNonQueryAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنهاء الاتصالات النشطة لقاعدة البيانات: {databaseName}");
                }
                catch (Exception killEx)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ تحذير في إنهاء الاتصالات: {killEx.Message}");
                }

                // انتظار قصير للتأكد من إنهاء الاتصالات
                await Task.Delay(2000);

                // الخطوة 2: تعيين قاعدة البيانات إلى وضع المستخدم الواحد
                var setSingleUserQuery = $@"
                    IF EXISTS (SELECT name FROM sys.databases WHERE name = '{databaseName}')
                    BEGIN
                        ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE
                    END";

                using var singleUserCommand = new Microsoft.Data.SqlClient.SqlCommand(setSingleUserQuery, connection);
                await singleUserCommand.ExecuteNonQueryAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم تعيين قاعدة البيانات إلى وضع المستخدم الواحد: {databaseName}");

                // انتظار قصير
                await Task.Delay(1000);

                // الخطوة 3: حذف قاعدة البيانات
                var dropDatabaseQuery = $@"
                    IF EXISTS (SELECT name FROM sys.databases WHERE name = '{databaseName}')
                    BEGIN
                        DROP DATABASE [{databaseName}]
                    END";

                using var dropCommand = new Microsoft.Data.SqlClient.SqlCommand(dropDatabaseQuery, connection);
                await dropCommand.ExecuteNonQueryAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم حذف قاعدة البيانات بنجاح بـ SQL: {databaseName}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل حذف قاعدة البيانات بـ SQL {databaseName}: {ex.Message}");

                // محاولة إضافية لإعادة تعيين قاعدة البيانات إلى وضع متعدد المستخدمين في حالة الفشل
                try
                {
                    var connectionString = @"Server=(LocalDB)\MSSQLLocalDB;Database=master;Trusted_Connection=True;Connection Timeout=10;";
                    using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                    await connection.OpenAsync();

                    var resetQuery = $@"
                        IF EXISTS (SELECT name FROM sys.databases WHERE name = '{databaseName}')
                        BEGIN
                            ALTER DATABASE [{databaseName}] SET MULTI_USER
                        END";

                    using var resetCommand = new Microsoft.Data.SqlClient.SqlCommand(resetQuery, connection);
                    await resetCommand.ExecuteNonQueryAsync();

                    System.Diagnostics.Debug.WriteLine($"✅ تم إعادة تعيين قاعدة البيانات إلى وضع متعدد المستخدمين: {databaseName}");
                }
                catch (Exception resetEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في إعادة تعيين قاعدة البيانات: {resetEx.Message}");
                }

                return false;
            }
        }

        /// <summary>
        /// حذف قاعدة البيانات باستخدام SqlLocalDB Command Line
        /// </summary>
        private static async Task<bool> DeleteDatabaseWithCommand(string databaseName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🗑️ محاولة حذف قاعدة البيانات بـ Command Line: {databaseName}");

                // الخطوة 1: إيقاف LocalDB
                var stopResult = await RunLocalDBCommand("stop MSSQLLocalDB");
                System.Diagnostics.Debug.WriteLine($"Stop LocalDB Result: {stopResult}");

                await Task.Delay(2000);

                // الخطوة 2: بدء LocalDB
                var startResult = await RunLocalDBCommand("start MSSQLLocalDB");
                System.Diagnostics.Debug.WriteLine($"Start LocalDB Result: {startResult}");

                await Task.Delay(2000);

                // الخطوة 3: محاولة حذف قاعدة البيانات مرة أخرى بـ SQL
                var connectionString = @"Server=(LocalDB)\MSSQLLocalDB;Database=master;Trusted_Connection=True;Connection Timeout=30;";
                using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                await connection.OpenAsync();

                var dropDatabaseQuery = $@"
                    IF EXISTS (SELECT name FROM sys.databases WHERE name = '{databaseName}')
                    BEGIN
                        ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
                        DROP DATABASE [{databaseName}];
                    END";

                using var dropCommand = new Microsoft.Data.SqlClient.SqlCommand(dropDatabaseQuery, connection);
                await dropCommand.ExecuteNonQueryAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم حذف قاعدة البيانات بنجاح بـ Command Line: {databaseName}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل حذف قاعدة البيانات بـ Command Line {databaseName}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تشغيل أمر SqlLocalDB
        /// </summary>
        private static async Task<string> RunLocalDBCommand(string arguments)
        {
            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sqllocaldb",
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();

                    if (!string.IsNullOrEmpty(error))
                    {
                        System.Diagnostics.Debug.WriteLine($"LocalDB Command Error: {error}");
                    }

                    return output;
                }

                return "";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تشغيل أمر LocalDB: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// حذف ملفات قاعدة البيانات مباشرة
        /// </summary>
        private static async Task<bool> DeleteDatabaseFiles(string databaseName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🗑️ محاولة حذف ملفات قاعدة البيانات: {databaseName}");

                // الحصول على مسار ملفات قاعدة البيانات
                var connectionString = @"Server=(LocalDB)\MSSQLLocalDB;Database=master;Trusted_Connection=True;Connection Timeout=10;";

                List<string> filePaths = new List<string>();

                try
                {
                    using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                    await connection.OpenAsync();

                    var getFilesQuery = $@"
                        SELECT physical_name
                        FROM sys.master_files
                        WHERE database_id = DB_ID('{databaseName}')";

                    using var command = new Microsoft.Data.SqlClient.SqlCommand(getFilesQuery, connection);
                    using var reader = await command.ExecuteReaderAsync();

                    while (await reader.ReadAsync())
                    {
                        filePaths.Add(reader.GetString(0));
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لا يمكن الحصول على مسارات الملفات: {ex.Message}");

                    // محاولة تخمين مسارات الملفات
                    var userProfile = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
                    var localDbPath = Path.Combine(userProfile, "AppData", "Local", "Microsoft", "Microsoft SQL Server Local DB", "Instances", "MSSQLLocalDB");

                    if (Directory.Exists(localDbPath))
                    {
                        var possibleFiles = Directory.GetFiles(localDbPath, $"{databaseName}.*", SearchOption.AllDirectories);
                        filePaths.AddRange(possibleFiles);
                    }
                }

                if (filePaths.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على ملفات قاعدة البيانات: {databaseName}");
                    return false;
                }

                // إيقاف LocalDB أولاً
                await RunLocalDBCommand("stop MSSQLLocalDB");
                await Task.Delay(3000);

                // حذف الملفات
                bool allDeleted = true;
                foreach (var filePath in filePaths)
                {
                    try
                    {
                        if (File.Exists(filePath))
                        {
                            File.Delete(filePath);
                            System.Diagnostics.Debug.WriteLine($"✅ تم حذف الملف: {filePath}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ فشل حذف الملف {filePath}: {ex.Message}");
                        allDeleted = false;
                    }
                }

                // إعادة تشغيل LocalDB
                await RunLocalDBCommand("start MSSQLLocalDB");
                await Task.Delay(2000);

                if (allDeleted)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم حذف جميع ملفات قاعدة البيانات: {databaseName}");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ تم حذف بعض ملفات قاعدة البيانات فقط: {databaseName}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل حذف ملفات قاعدة البيانات {databaseName}: {ex.Message}");

                // محاولة إعادة تشغيل LocalDB في حالة الخطأ
                try
                {
                    await RunLocalDBCommand("start MSSQLLocalDB");
                }
                catch { }

                return false;
            }
        }

        /// <summary>
        /// إعداد قاعدة البيانات بشكل آمن
        /// </summary>
        public static async Task<bool> SafeSetupDatabase()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء إعداد قاعدة البيانات الآمن...");

                var settings = LoadSettings();

                // إذا كانت قاعدة البيانات محلية، تحقق من وجود LocalDB
                if (settings.UseLocalDatabase)
                {
                    var localDBAvailable = await IsLocalDBAvailable();
                    if (!localDBAvailable)
                    {
                        System.Diagnostics.Debug.WriteLine("❌ LocalDB غير متوفر. يرجى تثبيت SQL Server LocalDB");
                        return false;
                    }
                    System.Diagnostics.Debug.WriteLine("✅ LocalDB متوفر");
                }

                // التحقق من إمكانية الاتصال أولاً
                var canConnect = await CanConnectToDatabase();
                if (canConnect)
                {
                    System.Diagnostics.Debug.WriteLine("✅ قاعدة البيانات متاحة والاتصال يعمل");
                    return true;
                }

                System.Diagnostics.Debug.WriteLine("⚠️ لا يمكن الاتصال - فحص وجود قاعدة البيانات...");

                // التحقق من وجود قاعدة البيانات
                var exists = await DatabaseExists();
                System.Diagnostics.Debug.WriteLine($"📊 حالة قاعدة البيانات: {(exists ? "موجودة" : "غير موجودة")}");

                if (!exists)
                {
                    // إنشاء قاعدة البيانات
                    var created = await CreateDatabaseIfNotExists();
                    if (!created)
                    {
                        System.Diagnostics.Debug.WriteLine("❌ فشل في إنشاء قاعدة البيانات");
                        return false;
                    }
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قاعدة البيانات بنجاح");
                }

                // إعادة فحص الاتصال بعد إنشاء قاعدة البيانات
                canConnect = await CanConnectToDatabase();
                if (!canConnect)
                {
                    System.Diagnostics.Debug.WriteLine("❌ لا يمكن الاتصال بقاعدة البيانات حتى بعد إنشائها");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إعداد قاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        public static async Task<bool> TestConnectionAsync()
        {
            try
            {
                var connectionString = GetConnectionString();
                using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                await connection.OpenAsync();
                System.Diagnostics.Debug.WriteLine("✅ تم اختبار الاتصال بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل اختبار الاتصال: {ex.Message}");
                return false;
            }
        }


    }
}
