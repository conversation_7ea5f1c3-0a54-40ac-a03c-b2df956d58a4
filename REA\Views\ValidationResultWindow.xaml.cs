using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة عرض نتائج التحقق من صحة البيانات
    /// </summary>
    public partial class ValidationResultWindow : Window, INotifyPropertyChanged
    {
        public ValidationResult ValidationResult { get; private set; }
        public ValidationAction UserAction { get; private set; } = ValidationAction.Cancel;

        public ValidationResultWindow(ValidationResult validationResult)
        {
            InitializeComponent();
            ValidationResult = validationResult;
            DataContext = this;
            
            System.Diagnostics.Debug.WriteLine($"✅ تم فتح نافذة نتائج التحقق - الأخطاء: {validationResult.Errors.Count}, التحذيرات: {validationResult.Warnings.Count}");
        }

        #region Properties for Binding

        public int QualityScore => ValidationResult.QualityScore;
        public string QualityLevel => ValidationResult.GetQualityLevel();
        public List<string> Errors => ValidationResult.Errors;
        public List<string> Warnings => ValidationResult.Warnings;
        public int ErrorsCount => ValidationResult.Errors.Count;
        public int WarningsCount => ValidationResult.Warnings.Count;
        public string IsValidText => ValidationResult.IsValid ? "نعم" : "لا";

        public Visibility ErrorsVisibility => ErrorsCount > 0 ? Visibility.Visible : Visibility.Collapsed;
        public Visibility WarningsVisibility => WarningsCount > 0 ? Visibility.Visible : Visibility.Collapsed;
        public Visibility SuccessVisibility => ValidationResult.IsValid && !ValidationResult.HasWarnings ? Visibility.Visible : Visibility.Collapsed;
        
        public Visibility ContinueButtonVisibility => ValidationResult.IsValid ? Visibility.Visible : Visibility.Collapsed;
        public Visibility FixButtonVisibility => !ValidationResult.IsValid ? Visibility.Visible : Visibility.Collapsed;
        public Visibility AcceptButtonVisibility => !ValidationResult.IsValid ? Visibility.Visible : Visibility.Collapsed; // زر القبول يظهر فقط عند وجود أخطاء

        #endregion

        private void ContinueButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UserAction = ValidationAction.Continue;
                DialogResult = true;
                Close();
                
                System.Diagnostics.Debug.WriteLine("✅ المستخدم اختار متابعة الاستيراد");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في متابعة الاستيراد: {ex.Message}");
            }
        }

        private void FixDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UserAction = ValidationAction.FixData;
                DialogResult = false;
                Close();
                
                System.Diagnostics.Debug.WriteLine("🔧 المستخدم اختار إصلاح البيانات");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح البيانات: {ex.Message}");
            }
        }

        private void AcceptWithErrorsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UserAction = ValidationAction.AcceptWithErrors;
                DialogResult = true;
                Close();

                System.Diagnostics.Debug.WriteLine("✅ المستخدم اختار قبول البيانات مع الأخطاء والمتابعة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في قبول البيانات مع الأخطاء: {ex.Message}");
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UserAction = ValidationAction.Cancel;
                DialogResult = false;
                Close();

                System.Diagnostics.Debug.WriteLine("❌ المستخدم ألغى العملية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إلغاء العملية: {ex.Message}");
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// إجراءات المستخدم في نافذة التحقق
    /// </summary>
    public enum ValidationAction
    {
        Continue,
        FixData,
        Cancel,
        AcceptWithErrors  // إضافة خيار القبول مع الأخطاء
    }
}
