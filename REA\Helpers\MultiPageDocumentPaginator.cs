using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Documents;
using System.Windows.Media;

namespace SFDSystem.Helpers
{
    /// <summary>
    /// DocumentPaginator مخصص لطباعة صفحات متعددة منفصلة
    /// </summary>
    public class MultiPageDocumentPaginator : DocumentPaginator
    {
        private readonly List<FrameworkElement> _pages;
        private readonly Size _pageSize;

        public MultiPageDocumentPaginator(List<System.Windows.Controls.Border> pages)
        {
            _pages = pages?.Cast<FrameworkElement>().ToList() ?? throw new ArgumentNullException(nameof(pages));

            // حجم صفحة A4 بالبكسل (96 DPI)
            // A4 = 210mm × 297mm = 8.27" × 11.69" = 794px × 1123px at 96 DPI
            _pageSize = new Size(794, 1123);

            System.Diagnostics.Debug.WriteLine($"📐 تم تحديد حجم الصفحة A4: {_pageSize.Width}×{_pageSize.Height} بكسل");
        }

        public override DocumentPage GetPage(int pageNumber)
        {
            try
            {
                if (pageNumber < 0 || pageNumber >= _pages.Count)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ رقم الصفحة {pageNumber + 1} خارج النطاق (المتاح: 1-{_pages.Count})");
                    return DocumentPage.Missing;
                }

                var page = _pages[pageNumber];

                System.Diagnostics.Debug.WriteLine($"📄 إنشاء صفحة PDF رقم {pageNumber + 1} من {_pages.Count}");

                // إنشاء نسخة من الصفحة للطباعة إذا كانت مرتبطة بـ parent
                FrameworkElement pageToRender = page;
                if (page.Parent != null)
                {
                    pageToRender = ClonePage(page);
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء نسخة من الصفحة {pageNumber + 1}");
                }

                // تحديد حجم الصفحة وترتيبها
                pageToRender.Measure(_pageSize);
                pageToRender.Arrange(new Rect(_pageSize));
                pageToRender.UpdateLayout();

                // إنشاء DrawingVisual للصفحة
                var visual = new DrawingVisual();
                using (var context = visual.RenderOpen())
                {
                    // رسم خلفية بيضاء
                    context.DrawRectangle(Brushes.White, null, new Rect(_pageSize));

                    // رسم محتوى الصفحة بطريقة بسيطة وموثوقة
                    var visualBrush = new VisualBrush(pageToRender)
                    {
                        Stretch = Stretch.Fill, // ملء الصفحة بالكامل
                        AlignmentX = AlignmentX.Center,
                        AlignmentY = AlignmentY.Top
                    };

                    context.DrawRectangle(visualBrush, null, new Rect(_pageSize));

                    // إضافة رقم الصفحة في أسفل اليمين بشكل احترافي
                    var pageNumberText = new FormattedText(
                        (pageNumber + 1).ToString(),
                        System.Globalization.CultureInfo.CurrentCulture,
                        FlowDirection.LeftToRight,
                        new Typeface("Arial"),
                        11,
                        new SolidColorBrush(Color.FromRgb(64, 64, 64)),
                        96);

                    // رسم إطار لرقم الصفحة في أسفل اليسار ومرفوع قليلاً
                    var pageNumberRect = new Rect(
                        5, // أسفل اليسار
                        _pageSize.Height - 45, // مرفوع قليلاً من 25 إلى 45
                        35, 20);

                    // خلفية رقم الصفحة
                    context.DrawRectangle(
                        new SolidColorBrush(Color.FromArgb(240, 255, 255, 255)),
                        new Pen(new SolidColorBrush(Color.FromArgb(100, 128, 128, 128)), 1),
                        pageNumberRect);

                    // رسم رقم الصفحة
                    context.DrawText(pageNumberText,
                        new Point(pageNumberRect.X + (pageNumberRect.Width - pageNumberText.Width) / 2,
                                 pageNumberRect.Y + (pageNumberRect.Height - pageNumberText.Height) / 2));
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء صفحة PDF رقم {pageNumber + 1} بنجاح");
                return new DocumentPage(visual, _pageSize, new Rect(_pageSize), new Rect(_pageSize));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الصفحة {pageNumber + 1}: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                return DocumentPage.Missing;
            }
        }

        private FrameworkElement ClonePage(FrameworkElement originalPage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 محاولة نسخ الصفحة...");

                // إنشاء نسخة من الصفحة للطباعة باستخدام XAML
                var xaml = System.Windows.Markup.XamlWriter.Save(originalPage);
                var clonedPage = (FrameworkElement)System.Windows.Markup.XamlReader.Parse(xaml);

                // تطبيق نفس الحجم والخصائص
                clonedPage.Width = originalPage.Width;
                clonedPage.Height = originalPage.Height;
                clonedPage.Margin = originalPage.Margin;

                System.Diagnostics.Debug.WriteLine($"✅ تم نسخ الصفحة بنجاح");
                return clonedPage;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ فشل في نسخ الصفحة، سيتم استخدام الأصلية: {ex.Message}");

                // في حالة فشل النسخ، قم بإزالة الصفحة من الـ parent مؤقتاً
                try
                {
                    if (originalPage.Parent is System.Windows.Controls.Panel parentPanel)
                    {
                        parentPanel.Children.Remove(originalPage);
                        System.Diagnostics.Debug.WriteLine($"🔄 تم إزالة الصفحة من الـ parent مؤقتاً للطباعة");
                    }
                }
                catch (Exception removeEx)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ فشل في إزالة الصفحة من الـ parent: {removeEx.Message}");
                }

                return originalPage;
            }
        }

        public override bool IsPageCountValid => true;

        public override int PageCount => _pages?.Count ?? 0;

        public override Size PageSize
        {
            get => _pageSize;
            set { /* حجم الصفحة ثابت */ }
        }

        public override IDocumentPaginatorSource Source => null;
    }
}
