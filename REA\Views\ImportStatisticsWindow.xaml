<Window x:Class="DriverManagementSystem.Views.ImportStatisticsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="📊 إحصائيات استيراد Excel المتقدمة" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Window.Resources>
        <Style TargetType="Border" x:Key="StatCardStyle">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="TextBlock" x:Key="StatNumberStyle">
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10"/>
        </Style>

        <Style TargetType="TextBlock" x:Key="StatLabelStyle">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="#666"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Linear Gradient(45deg, #667eea 0%, #764ba2 100%)" 
                Padding="20" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="📊 إحصائيات استيراد Excel المتقدمة" 
                          FontSize="24" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="مراقبة شاملة لجميع عمليات الاستيراد في النظام" 
                          FontSize="14" Foreground="#E8E8E8" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Statistics Cards -->
        <Grid Grid.Row="1" Margin="20,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Imports -->
            <Border Grid.Column="0" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="📈" FontSize="40" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Statistics.TotalImports}" 
                              Style="{StaticResource StatNumberStyle}" Foreground="#2196F3"/>
                    <TextBlock Text="إجمالي العمليات" Style="{StaticResource StatLabelStyle}"/>
                </StackPanel>
            </Border>

            <!-- Success Rate -->
            <Border Grid.Column="1" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="✅" FontSize="40" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Statistics.SuccessRate, StringFormat={}{0:F1}%}" 
                              Style="{StaticResource StatNumberStyle}" Foreground="#4CAF50"/>
                    <TextBlock Text="معدل النجاح" Style="{StaticResource StatLabelStyle}"/>
                </StackPanel>
            </Border>

            <!-- Today's Imports -->
            <Border Grid.Column="2" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="📅" FontSize="40" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Statistics.TodayImports}" 
                              Style="{StaticResource StatNumberStyle}" Foreground="#FF9800"/>
                    <TextBlock Text="اليوم" Style="{StaticResource StatLabelStyle}"/>
                </StackPanel>
            </Border>

            <!-- Failed Imports -->
            <Border Grid.Column="3" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="❌" FontSize="40" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Statistics.FailedImports}" 
                              Style="{StaticResource StatNumberStyle}" Foreground="#F44336"/>
                    <TextBlock Text="العمليات الفاشلة" Style="{StaticResource StatLabelStyle}"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Recent Imports List -->
        <Border Grid.Row="2" Background="White" CornerRadius="10" Margin="20" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="📋 آخر عمليات الاستيراد" FontSize="18" FontWeight="Bold" Foreground="#333"/>
                    <Button Content="🔄 تحديث" Margin="20,0,0,0" Padding="10,5" 
                           Background="#2196F3" Foreground="White" BorderThickness="0"
                           Click="RefreshButton_Click"/>
                    <Button Content="🗑️ مسح السجل" Margin="10,0,0,0" Padding="10,5" 
                           Background="#F44336" Foreground="White" BorderThickness="0"
                           Click="ClearLogButton_Click"/>
                </StackPanel>

                <DataGrid Grid.Row="1" Name="ImportsDataGrid" 
                         ItemsSource="{Binding RecentImports}"
                         AutoGenerateColumns="False" 
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         AlternatingRowBackground="#F8F9FA">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الوقت" Binding="{Binding Timestamp, StringFormat=dd/MM/yyyy HH:mm}" Width="120"/>
                        <DataGridTextColumn Header="اسم الملف" Binding="{Binding FileName}" Width="200"/>
                        <DataGridTextColumn Header="الرقم المرجعي" Binding="{Binding IndexValue}" Width="100"/>
                        <DataGridTextColumn Header="رقم الزيارة" Binding="{Binding VisitNumber}" Width="120"/>
                        <DataGridTextColumn Header="المشاريع" Binding="{Binding ProjectsCount}" Width="80"/>
                        <DataGridTextColumn Header="خط السير" Binding="{Binding ItineraryDaysCount}" Width="80"/>
                        
                        <DataGridTemplateColumn Header="الحالة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="15" Padding="8,4" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="Success">
                                                        <Setter Property="Background" Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Failed">
                                                        <Setter Property="Background" Value="#FFEBEE"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock HorizontalAlignment="Center" FontWeight="Bold">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="Success">
                                                            <Setter Property="Text" Value="✅ نجح"/>
                                                            <Setter Property="Foreground" Value="#2E7D32"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="Failed">
                                                            <Setter Property="Text" Value="❌ فشل"/>
                                                            <Setter Property="Foreground" Value="#C62828"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="وقت المعالجة" Binding="{Binding ProcessingTime, StringFormat={}{0:F1}s}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="3" Background="#F5F5F5" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="آخر تحديث:" FontWeight="Bold" Margin="0,0,10,0"/>
                <TextBlock Text="{Binding LastUpdateTime, StringFormat=dd/MM/yyyy HH:mm:ss}"/>
                <TextBlock Text="•" Margin="15,0"/>
                <TextBlock Text="إجمالي الأسبوع:" FontWeight="Bold" Margin="0,0,10,0"/>
                <TextBlock Text="{Binding Statistics.WeekImports}"/>
                <TextBlock Text="•" Margin="15,0"/>
                <TextBlock Text="إجمالي الشهر:" FontWeight="Bold" Margin="0,0,10,0"/>
                <TextBlock Text="{Binding Statistics.MonthImports}"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
