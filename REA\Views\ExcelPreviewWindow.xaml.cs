using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة معاينة البيانات المستوردة من Excel
    /// </summary>
    public partial class ExcelPreviewWindow : Window
    {
        public bool IsConfirmed { get; private set; } = false;
        public FieldVisitImportResult ImportResult { get; private set; }

        public ExcelPreviewWindow(FieldVisitImportResult importResult)
        {
            InitializeComponent();
            ImportResult = importResult;
            SetupPreviewData();
        }

        private void SetupPreviewData()
        {
            try
            {
                // إعداد بيانات المعاينة
                var previewData = new ExcelPreviewData
                {
                    VisitData = ImportResult.VisitData,
                    Projects = ImportResult.Projects,
                    Itinerary = ImportResult.Itinerary.Select(item => new ItineraryPreviewItem
                    {
                        DayNumber = item.DayNumber,
                        Plan = item.Plan
                    }).ToList()
                };

                DataContext = previewData;

                // تحديث عنوان النافذة
                Title = $"معاينة بيانات Excel - الرقم المرجعي: {ImportResult.VisitData?.VisitFormNumber ?? "غير محدد"}";

                System.Diagnostics.Debug.WriteLine("✅ تم إعداد بيانات المعاينة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد بيانات المعاينة: {ex.Message}");
                MessageBox.Show($"خطأ في إعداد بيانات المعاينة:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                IsConfirmed = true;
                DialogResult = true;
                Close();

                System.Diagnostics.Debug.WriteLine("✅ تم تأكيد استيراد البيانات من المعاينة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تأكيد الاستيراد: {ex.Message}");
                MessageBox.Show($"خطأ في تأكيد الاستيراد:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                IsConfirmed = false;
                DialogResult = false;
                Close();

                System.Diagnostics.Debug.WriteLine("❌ تم إلغاء استيراد البيانات من المعاينة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إلغاء الاستيراد: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// نموذج بيانات المعاينة
    /// </summary>
    public class ExcelPreviewData
    {
        public VisitImportData? VisitData { get; set; }
        public List<ProjectImportData> Projects { get; set; } = new();
        public List<ItineraryPreviewItem> Itinerary { get; set; } = new();
    }

    /// <summary>
    /// عنصر خط السير للمعاينة
    /// </summary>
    public class ItineraryPreviewItem
    {
        public int DayNumber { get; set; }
        public string Plan { get; set; } = string.Empty;
    }
}
