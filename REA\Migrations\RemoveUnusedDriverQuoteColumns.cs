using System;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;

namespace SFDSystem.Migrations
{
    /// <summary>
    /// Migration لحذف الأعمدة غير المستخدمة من جدول DriverQuotes
    /// </summary>
    public static class RemoveUnusedDriverQuoteColumns
    {
        /// <summary>
        /// تطبيق Migration لحذف الأعمدة غير المرغوب فيها
        /// </summary>
        public static async Task<bool> ApplyAsync(string connectionString)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء حذف الأعمدة غير المستخدمة من جدول DriverQuotes...");

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // قائمة الأعمدة المراد حذفها
                var columnsToRemove = new[]
                {
                    "DriverId",
                    "DriverCode", 
                    "PhoneNumber",
                    "VehicleNumber",
                    "VehicleType"
                };

                foreach (var columnName in columnsToRemove)
                {
                    try
                    {
                        // التحقق من وجود العمود أولاً
                        var checkColumnSql = @"
                            SELECT COUNT(*) 
                            FROM INFORMATION_SCHEMA.COLUMNS 
                            WHERE TABLE_NAME = 'DriverQuotes' AND COLUMN_NAME = @ColumnName
                        ";

                        using var checkCommand = new SqlCommand(checkColumnSql, connection);
                        checkCommand.Parameters.AddWithValue("@ColumnName", columnName);
                        var columnExists = (int)await checkCommand.ExecuteScalarAsync() > 0;

                        if (columnExists)
                        {
                            // حذف الفهارس المرتبطة بالعمود أولاً (إن وجدت)
                            if (columnName == "DriverId")
                            {
                                try
                                {
                                    var dropIndexSql = "DROP INDEX IF EXISTS IX_DriverQuote_DriverId ON DriverQuotes";
                                    using var dropIndexCommand = new SqlCommand(dropIndexSql, connection);
                                    await dropIndexCommand.ExecuteNonQueryAsync();
                                    System.Diagnostics.Debug.WriteLine($"✅ تم حذف فهرس IX_DriverQuote_DriverId");
                                }
                                catch (Exception indexEx)
                                {
                                    System.Diagnostics.Debug.WriteLine($"⚠️ تحذير: فشل في حذف الفهرس: {indexEx.Message}");
                                }
                            }

                            // حذف العمود
                            var dropColumnSql = $"ALTER TABLE DriverQuotes DROP COLUMN [{columnName}]";
                            using var dropCommand = new SqlCommand(dropColumnSql, connection);
                            await dropCommand.ExecuteNonQueryAsync();
                            System.Diagnostics.Debug.WriteLine($"✅ تم حذف العمود {columnName} بنجاح");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"ℹ️ العمود {columnName} غير موجود (تم حذفه مسبقاً)");
                        }
                    }
                    catch (Exception columnEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف العمود {columnName}: {columnEx.Message}");
                        // لا نتوقف عند فشل حذف عمود واحد، نكمل مع الأعمدة الأخرى
                    }
                }

                await connection.CloseAsync();
                System.Diagnostics.Debug.WriteLine("✅ تم إكمال حذف الأعمدة غير المستخدمة");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف الأعمدة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التراجع عن Migration (إضافة الأعمدة مرة أخرى)
        /// </summary>
        public static async Task<bool> RollbackAsync(string connectionString)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء التراجع عن حذف الأعمدة...");

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // إضافة الأعمدة مرة أخرى (في حالة الحاجة للتراجع)
                var addColumnsSql = @"
                    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'DriverQuotes' AND COLUMN_NAME = 'DriverId')
                        ALTER TABLE DriverQuotes ADD DriverId INT NULL;
                    
                    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'DriverQuotes' AND COLUMN_NAME = 'DriverCode')
                        ALTER TABLE DriverQuotes ADD DriverCode NVARCHAR(20) NULL;
                    
                    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'DriverQuotes' AND COLUMN_NAME = 'PhoneNumber')
                        ALTER TABLE DriverQuotes ADD PhoneNumber NVARCHAR(20) NULL;
                    
                    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'DriverQuotes' AND COLUMN_NAME = 'VehicleNumber')
                        ALTER TABLE DriverQuotes ADD VehicleNumber NVARCHAR(20) NULL;
                    
                    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'DriverQuotes' AND COLUMN_NAME = 'VehicleType')
                        ALTER TABLE DriverQuotes ADD VehicleType NVARCHAR(50) NULL;
                ";

                using var command = new SqlCommand(addColumnsSql, connection);
                await command.ExecuteNonQueryAsync();

                await connection.CloseAsync();
                System.Diagnostics.Debug.WriteLine("✅ تم التراجع عن حذف الأعمدة بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التراجع عن حذف الأعمدة: {ex.Message}");
                return false;
            }
        }
    }
}
