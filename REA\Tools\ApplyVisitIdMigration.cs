using System;
using System.Threading.Tasks;
using DriverManagementSystem.Helpers;

namespace DriverManagementSystem.Tools
{
    /// <summary>
    /// أداة لتطبيق Migration عمود VisitId
    /// </summary>
    public class ApplyVisitIdMigration
    {
        public static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("🔄 بدء تطبيق Migration لإضافة عمود VisitId...");
                
                await VisitIdMigrationHelper.ApplyVisitIdMigrationAsync();
                
                Console.WriteLine("✅ تم تطبيق Migration بنجاح!");
                Console.WriteLine("اضغط أي مفتاح للخروج...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ: {ex.Message}");
                Console.WriteLine("اضغط أي مفتاح للخروج...");
                Console.ReadKey();
            }
        }
    }
}
