using System;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using DriverManagementSystem.Views;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة الطباعة الاحترافية مع إعدادات متقدمة
    /// </summary>
    public static class AdvancedPrintService
    {
        /// <summary>
        /// فتح نافذة إعدادات الطباعة وتنفيذ الطباعة
        /// </summary>
        /// <param name="elementToPrint">العنصر المراد طباعته</param>
        /// <param name="documentTitle">عنوان المستند</param>
        /// <param name="owner">النافذة الأصلية</param>
        /// <returns>true إذا تم تنفيذ الطباعة، false إذا تم الإلغاء</returns>
        public static bool ShowPrintDialog(FrameworkElement elementToPrint, string documentTitle = "مستند", Window owner = null)
        {
            try
            {
                var printSettingsWindow = new PrintSettingsWindow(elementToPrint, documentTitle);
                
                if (owner != null)
                {
                    printSettingsWindow.Owner = owner;
                }

                return printSettingsWindow.ShowDialog() == true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إعدادات الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// طباعة سريعة بإعدادات افتراضية
        /// </summary>
        /// <param name="elementToPrint">العنصر المراد طباعته</param>
        /// <param name="documentTitle">عنوان المستند</param>
        /// <returns>true إذا تم تنفيذ الطباعة بنجاح</returns>
        public static bool QuickPrint(FrameworkElement elementToPrint, string documentTitle = "مستند")
        {
            try
            {
                var printDialog = new PrintDialog();
                
                // إعدادات افتراضية
                SetDefaultPrintSettings(printDialog);

                if (printDialog.ShowDialog() == true)
                {
                    printDialog.PrintVisual(elementToPrint, documentTitle);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة السريعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// طباعة مباشرة بدون حوار (للطابعة الافتراضية)
        /// </summary>
        /// <param name="elementToPrint">العنصر المراد طباعته</param>
        /// <param name="documentTitle">عنوان المستند</param>
        /// <returns>true إذا تم تنفيذ الطباعة بنجاح</returns>
        public static bool DirectPrint(FrameworkElement elementToPrint, string documentTitle = "مستند")
        {
            try
            {
                var printDialog = new PrintDialog();
                
                // إعدادات افتراضية
                SetDefaultPrintSettings(printDialog);

                // طباعة مباشرة بدون عرض الحوار
                printDialog.PrintVisual(elementToPrint, documentTitle);
                
                MessageBox.Show("تم إرسال المستند للطباعة بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة المباشرة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// طباعة إلى PDF
        /// </summary>
        /// <param name="elementToPrint">العنصر المراد طباعته</param>
        /// <param name="documentTitle">عنوان المستند</param>
        /// <param name="filePath">مسار ملف PDF (اختياري)</param>
        /// <returns>true إذا تم تنفيذ التصدير بنجاح</returns>
        public static bool PrintToPdf(FrameworkElement elementToPrint, string documentTitle = "مستند", string filePath = null)
        {
            try
            {
                var printDialog = new PrintDialog();
                
                // البحث عن طابعة Microsoft Print to PDF
                var pdfPrinter = FindPdfPrinter();
                if (pdfPrinter != null)
                {
                    printDialog.PrintQueue = pdfPrinter;
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على طابعة PDF. تأكد من تثبيت Microsoft Print to PDF", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                // إعدادات PDF
                SetDefaultPrintSettings(printDialog);

                if (string.IsNullOrEmpty(filePath))
                {
                    // عرض حوار حفظ الملف
                    var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                    {
                        Filter = "PDF Files (*.pdf)|*.pdf",
                        DefaultExt = "pdf",
                        FileName = $"{documentTitle}.pdf"
                    };

                    if (saveFileDialog.ShowDialog() == true)
                    {
                        filePath = saveFileDialog.FileName;
                    }
                    else
                    {
                        return false;
                    }
                }

                // طباعة إلى PDF
                printDialog.PrintVisual(elementToPrint, documentTitle);
                
                MessageBox.Show($"تم تصدير المستند إلى PDF بنجاح:\n{filePath}", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// تعيين إعدادات الطباعة الافتراضية
        /// </summary>
        /// <param name="printDialog">حوار الطباعة</param>
        private static void SetDefaultPrintSettings(PrintDialog printDialog)
        {
            try
            {
                // حجم الورق A4
                printDialog.PrintTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA4);
                
                // اتجاه عمودي
                printDialog.PrintTicket.PageOrientation = PageOrientation.Portrait;
                
                // طباعة ملونة
                printDialog.PrintTicket.OutputColor = OutputColor.Color;
                
                // جودة عالية
                printDialog.PrintTicket.OutputQuality = OutputQuality.Normal;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"تعذر تطبيق الإعدادات الافتراضية: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث عن طابعة PDF
        /// </summary>
        /// <returns>طابعة PDF إذا وجدت، null إذا لم توجد</returns>
        private static PrintQueue FindPdfPrinter()
        {
            try
            {
                var printServer = new LocalPrintServer();
                var printQueues = printServer.GetPrintQueues();

                foreach (var printQueue in printQueues)
                {
                    if (printQueue.Name.Contains("PDF") || 
                        printQueue.Name.Contains("Microsoft Print to PDF") ||
                        printQueue.Name.Contains("Adobe PDF"))
                    {
                        return printQueue;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن طابعة PDF: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// التحقق من توفر الطابعات
        /// </summary>
        /// <returns>true إذا كانت هناك طابعات متاحة</returns>
        public static bool ArePrintersAvailable()
        {
            try
            {
                var printServer = new LocalPrintServer();
                var printQueues = printServer.GetPrintQueues();
                
                foreach (var printQueue in printQueues)
                {
                    return true; // إذا وجدت طابعة واحدة على الأقل
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على قائمة الطابعات المتاحة
        /// </summary>
        /// <returns>قائمة بأسماء الطابعات</returns>
        public static string[] GetAvailablePrinters()
        {
            try
            {
                var printers = new System.Collections.Generic.List<string>();
                var printServer = new LocalPrintServer();
                var printQueues = printServer.GetPrintQueues();

                foreach (var printQueue in printQueues)
                {
                    printers.Add(printQueue.Name);
                }

                return printers.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على قائمة الطابعات: {ex.Message}");
                return new string[0];
            }
        }
    }
}
