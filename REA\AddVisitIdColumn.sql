-- إضافة عمود VisitId إلى جدول FieldVisits
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[FieldVisits]') AND name = 'VisitId')
BEGIN
    ALTER TABLE [FieldVisits] ADD [VisitId] int NOT NULL DEFAULT 0;
    PRINT 'تم إضافة عمود VisitId إلى جدول FieldVisits';
END
ELSE
BEGIN
    PRINT 'عمود VisitId موجود بالفعل في جدول FieldVisits';
END

-- إضافة عمود VisitId إلى جدول DriverQuotes
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DriverQuotes]') AND name = 'VisitId')
BEGIN
    ALTER TABLE [DriverQuotes] ADD [VisitId] int NOT NULL DEFAULT 0;
    PRINT 'تم إضافة عمود VisitId إلى جدول DriverQuotes';
END
ELSE
BEGIN
    PRINT 'عمود VisitId موجود بالفعل في جدول DriverQuotes';
END

-- تحديث VisitId في جدول FieldVisits ليكون مساوياً لـ Id
UPDATE [FieldVisits] 
SET [VisitId] = [Id] 
WHERE [VisitId] = 0 OR [VisitId] IS NULL;
PRINT 'تم تحديث VisitId في جدول FieldVisits';

-- تحديث VisitId في جدول DriverQuotes بناءً على VisitNumber
UPDATE dq 
SET dq.[VisitId] = fv.[VisitId]
FROM [DriverQuotes] dq
INNER JOIN [FieldVisits] fv ON dq.[VisitNumber] = fv.[VisitNumber]
WHERE dq.[VisitId] = 0 OR dq.[VisitId] IS NULL;
PRINT 'تم تحديث VisitId في جدول DriverQuotes';

-- إنشاء فهرس على VisitId لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_DriverQuotes_VisitId')
BEGIN
    CREATE INDEX IX_DriverQuotes_VisitId ON [DriverQuotes] ([VisitId]);
    PRINT 'تم إنشاء فهرس على VisitId في جدول DriverQuotes';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_FieldVisits_VisitId')
BEGIN
    CREATE INDEX IX_FieldVisits_VisitId ON [FieldVisits] ([VisitId]);
    PRINT 'تم إنشاء فهرس على VisitId في جدول FieldVisits';
END

-- التحقق من النتائج
SELECT 'FieldVisits' as TableName, COUNT(*) as RecordCount, 
       COUNT(CASE WHEN VisitId > 0 THEN 1 END) as WithVisitId
FROM [FieldVisits]
UNION ALL
SELECT 'DriverQuotes' as TableName, COUNT(*) as RecordCount,
       COUNT(CASE WHEN VisitId > 0 THEN 1 END) as WithVisitId  
FROM [DriverQuotes];

PRINT 'تم الانتهاء من إضافة عمود VisitId بنجاح!';
