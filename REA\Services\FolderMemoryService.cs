using System;
using System.IO;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة حفظ واسترجاع آخر مجلد تم اختياره
    /// </summary>
    public class FolderMemoryService
    {
        private readonly string _settingsFilePath;

        public FolderMemoryService()
        {
            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SFDSystem");
            Directory.CreateDirectory(appDataPath);
            _settingsFilePath = Path.Combine(appDataPath, "LastFolder.txt");
        }

        /// <summary>
        /// حفظ آخر مجلد تم اختياره
        /// </summary>
        public void SaveLastFolder(string folderPath)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(folderPath) && Directory.Exists(folderPath))
                {
                    File.WriteAllText(_settingsFilePath, folderPath);
                    System.Diagnostics.Debug.WriteLine($"💾 تم حفظ آخر مجلد: {folderPath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ آخر مجلد: {ex.Message}");
            }
        }

        /// <summary>
        /// استرجاع آخر مجلد تم اختياره
        /// </summary>
        public string GetLastFolder()
        {
            try
            {
                if (File.Exists(_settingsFilePath))
                {
                    var lastFolder = File.ReadAllText(_settingsFilePath).Trim();
                    if (!string.IsNullOrWhiteSpace(lastFolder) && Directory.Exists(lastFolder))
                    {
                        System.Diagnostics.Debug.WriteLine($"📂 تم استرجاع آخر مجلد: {lastFolder}");
                        return lastFolder;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استرجاع آخر مجلد: {ex.Message}");
            }

            // إرجاع مجلد سطح المكتب كافتراضي
            var defaultFolder = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            System.Diagnostics.Debug.WriteLine($"📂 استخدام المجلد الافتراضي: {defaultFolder}");
            return defaultFolder;
        }

        /// <summary>
        /// استخراج مجلد من مسار ملف
        /// </summary>
        public string GetFolderFromPath(string filePath)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(filePath))
                {
                    var folder = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrWhiteSpace(folder) && Directory.Exists(folder))
                    {
                        return folder;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استخراج المجلد من المسار: {ex.Message}");
            }

            return GetLastFolder();
        }
    }
}
