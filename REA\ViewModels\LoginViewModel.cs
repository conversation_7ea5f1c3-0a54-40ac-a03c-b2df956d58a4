using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.ViewModels
{
    public class LoginViewModel : BindableBase
    {
        private readonly IAuthenticationService _authService;
        private string _username = string.Empty;
        private string _password = string.Empty;
        private bool _isLoading;
        private string _errorMessage = string.Empty;
        public LoginViewModel(IAuthenticationService authService)
        {
            _authService = authService;
            LoginCommand = new DelegateCommand(async () => await LoginAsync(), CanLogin);
        }

        public string Username
        {
            get => _username;
            set
            {
                SetProperty(ref _username, value);
                LoginCommand.RaiseCanExecuteChanged();
                ClearError();
            }
        }

        public string Password
        {
            get => _password;
            set
            {
                SetProperty(ref _password, value);
                LoginCommand.RaiseCanExecuteChanged();
                ClearError();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public DelegateCommand LoginCommand { get; }

        public event EventHandler<bool>? LoginCompleted;

        private bool CanLogin()
        {
            return !string.IsNullOrWhiteSpace(Username) && 
                   !string.IsNullOrWhiteSpace(Password) && 
                   !IsLoading;
        }

        private async Task LoginAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔐 بدء عملية تسجيل الدخول...");
                IsLoading = true;
                ErrorMessage = string.Empty;

                System.Diagnostics.Debug.WriteLine($"🔐 محاولة تسجيل الدخول للمستخدم: {Username}");
                var user = await _authService.LoginAsync(Username, Password);

                if (user != null)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم تسجيل الدخول بنجاح");
                    LoginCompleted?.Invoke(this, true);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل تسجيل الدخول - بيانات غير صحيحة");
                    ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تسجيل الدخول: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.ToString()}");
                ErrorMessage = $"حدث خطأ أثناء تسجيل الدخول: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                System.Diagnostics.Debug.WriteLine("🔐 انتهاء عملية تسجيل الدخول");
            }
        }

        private void ClearError()
        {
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                ErrorMessage = string.Empty;
            }
        }
    }
}
