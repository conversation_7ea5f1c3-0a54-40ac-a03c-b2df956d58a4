<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <StartupObject>DriverManagementSystem.Program</StartupObject>
    <ApplicationManifest>app.manifest</ApplicationManifest>

    <AssemblyTitle>نظام إدارة الزيارات الميدانية - الصندوق الاجتماعي للتنمية</AssemblyTitle>
    <AssemblyDescription>نظام إدارة الزيارات الميدانية والتقارير - فرع ذمار والبيضاء</AssemblyDescription>
    <AssemblyCompany>الصندوق الاجتماعي للتنمية - فرع ذمار والبيضاء</AssemblyCompany>
    <AssemblyProduct>SFD Field Visits Management System</AssemblyProduct>
    <AssemblyCopyright>© 2025 الصندوق الاجتماعي للتنمية</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.104.1" />
    <PackageReference Include="iTextSharp.LGPLv2.Core" Version="3.7.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
    <PackageReference Include="PdfSharp" Version="6.0.0" />
    <PackageReference Include="Prism.Wpf" Version="9.0.537" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
  </ItemGroup>

  <!-- Include Icons as embedded resources -->
  <ItemGroup>
    <Resource Include="Icons\**\*" />
  </ItemGroup>





</Project>
