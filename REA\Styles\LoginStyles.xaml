<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:DriverManagementSystem.Converters">

    <!-- Login Window Specific Styles -->
    
    <!-- Primary Colors -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="#E3F2FD"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#1976D2"/>
    
    <!-- Text Colors -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>
    <SolidColorBrush x:Key="TextHintBrush" Color="#BDBDBD"/>
    
    <!-- Status Colors -->
    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
    
    <!-- Background Colors -->
    <SolidColorBrush x:Key="BackgroundBrush" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
    
    <!-- Login Button Style -->
    <Style x:Key="LoginButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Height" Value="50"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="8"
                            Padding="20,12">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#1565C0"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="#BDBDBD"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Input Field Style -->
    <Style x:Key="LoginInputStyle" TargetType="TextBox">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="12,10"/>
        <Setter Property="BorderBrush" Value="{StaticResource TextHintBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Margin="{TemplateBinding Padding}"
                                    VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter Property="BorderThickness" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Password Box Style -->
    <Style x:Key="LoginPasswordStyle" TargetType="PasswordBox">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="12,10"/>
        <Setter Property="BorderBrush" Value="{StaticResource TextHintBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="PasswordBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Margin="{TemplateBinding Padding}"
                                    VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter Property="BorderThickness" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Card Style -->
    <Style x:Key="LoginCardStyle" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="30"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Gray" 
                                Direction="270" 
                                ShadowDepth="3" 
                                Opacity="0.2" 
                                BlurRadius="15"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Converters -->
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
    <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
    <converters:BooleanToIconConverter x:Key="BooleanToIconConverter"/>
    <converters:LoadingTextConverter x:Key="LoadingTextConverter"/>
    <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
    
    <!-- Custom Converter for String to Visibility -->
    <Style x:Key="ErrorMessageStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFEBEE"/>
        <Setter Property="BorderBrush" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="10"/>
        <Setter Property="Margin" Value="0,0,0,15"/>
    </Style>
    
    <!-- Info Card Style -->
    <Style x:Key="InfoCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#E3F2FD"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="Margin" Value="0,15,0,0"/>
    </Style>
    
    <!-- Quick Action Style -->
    <Style x:Key="QuickActionStyle" TargetType="Border">
        <Setter Property="Background" Value="#F5F5F5"/>
        <Setter Property="CornerRadius" Value="6"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>
    
    <!-- Clock Style -->
    <Style x:Key="ClockStyle" TargetType="Border">
        <Setter Property="Background" Value="#E8F5E8"/>
        <Setter Property="CornerRadius" Value="6"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="0,0,0,20"/>
    </Style>

</ResourceDictionary>
