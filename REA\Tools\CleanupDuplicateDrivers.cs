using System;
using System.Threading.Tasks;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Tools
{
    /// <summary>
    /// أداة لتنظيف السائقين المكررين من قاعدة البيانات
    /// </summary>
    public static class CleanupDuplicateDrivers
    {
        /// <summary>
        /// تشغيل عملية تنظيف السائقين المكررين
        /// </summary>
        public static async Task RunCleanupAsync()
        {
            try
            {
                Console.WriteLine("🧹 بدء عملية تنظيف السائقين المكررين...");
                Console.WriteLine(new string('=', 50));

                using var dataService = new DatabaseService();

                // إزالة السائقين المكررين
                var removedCount = await dataService.RemoveDuplicateDriversAsync();

                Console.WriteLine(new string('=', 50));
                if (removedCount > 0)
                {
                    Console.WriteLine($"✅ تم حذف {removedCount} سائق مكرر بنجاح!");
                    Console.WriteLine("🔄 يُنصح بإعادة تشغيل التطبيق لرؤية التحديثات");
                }
                else
                {
                    Console.WriteLine("✅ قاعدة البيانات نظيفة - لا توجد سائقين مكررين");
                }
                
                // عرض إحصائيات نهائية
                var finalDrivers = await dataService.GetDriversAsync();
                Console.WriteLine($"📊 العدد النهائي للسائقين: {finalDrivers.Count}");
                
                Console.WriteLine(new string('=', 50));
                Console.WriteLine("✅ انتهت عملية التنظيف بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في عملية التنظيف: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// فحص السائقين المكررين بدون حذف
        /// </summary>
        public static async Task CheckDuplicatesAsync()
        {
            try
            {
                Console.WriteLine("🔍 فحص السائقين المكررين...");
                Console.WriteLine(new string('=', 50));

                using var dataService = new DatabaseService();
                
                // جلب جميع السائقين مباشرة من قاعدة البيانات
                var allDrivers = await dataService.GetDriversAsync();
                Console.WriteLine($"📊 إجمالي السائقين (بعد إزالة التكرار): {allDrivers.Count}");
                
                Console.WriteLine(new string('=', 50));
                Console.WriteLine("✅ انتهى الفحص");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الفحص: {ex.Message}");
            }
        }
    }
}
