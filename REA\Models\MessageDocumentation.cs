using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace DriverManagementSystem.Models
{
    /// <summary>
    /// نموذج توثيق الرسائل النصية للنزول الميداني - تصميم احترافي متطور
    /// </summary>
    public class MessageDocumentation
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// رقم الزيارة الميدانية (الأساس)
        /// </summary>
        [Required]
        public string VisitNumber { get; set; }

        /// <summary>
        /// القائم بالزيارة الأول
        /// </summary>
        public string VisitConductor1 { get; set; }

        /// <summary>
        /// القائم بالزيارة الثاني
        /// </summary>
        public string VisitConductor2 { get; set; }

        /// <summary>
        /// القائم بالزيارة الثالث
        /// </summary>
        public string VisitConductor3 { get; set; }

        /// <summary>
        /// تاريخ التوثيق
        /// </summary>
        public DateTime DocumentationDate { get; set; } = DateTime.Now;

        /// <summary>
        /// رقم التقرير
        /// </summary>
        public string ReportNumber { get; set; }

        /// <summary>
        /// المختص بالبترول الأول
        /// </summary>
        public string FirstOfficer { get; set; }

        /// <summary>
        /// المختص بالبترول الثاني
        /// </summary>
        public string SecondOfficer { get; set; }

        /// <summary>
        /// المختص بالبترول الثالث
        /// </summary>
        public string ThirdOfficer { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// حالة التوثيق
        /// </summary>
        public string Status { get; set; } = "مسودة";

        /// <summary>
        /// مسار مجلد الصور الرئيسي
        /// </summary>
        public string ImagesFolderPath { get; set; }

        /// <summary>
        /// مسار الصورة الأولى
        /// </summary>
        public string ImagePath1 { get; set; }

        /// <summary>
        /// مسار الصورة الثانية
        /// </summary>
        public string ImagePath2 { get; set; }

        /// <summary>
        /// مسار الصورة الثالثة
        /// </summary>
        public string ImagePath3 { get; set; }

        /// <summary>
        /// مسار الصورة الرابعة
        /// </summary>
        public string ImagePath4 { get; set; }

        /// <summary>
        /// مسار الصورة الخامسة
        /// </summary>
        public string ImagePath5 { get; set; }

        /// <summary>
        /// مسار الصورة السادسة
        /// </summary>
        public string ImagePath6 { get; set; }

        /// <summary>
        /// عدد الصور المحملة
        /// </summary>
        public int ImagesCount { get; set; } = 0;

        /// <summary>
        /// المرفقات الإضافية (غير الصور)
        /// </summary>
        public virtual ICollection<MessageAttachment> Attachments { get; set; } = new List<MessageAttachment>();

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModified { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نموذج مرفقات التوثيق
    /// </summary>
    public class MessageAttachment
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف التوثيق
        /// </summary>
        public int MessageDocumentationId { get; set; }

        /// <summary>
        /// اسم الملف
        /// </summary>
        [Required]
        public string FileName { get; set; }

        /// <summary>
        /// مسار الملف
        /// </summary>
        [Required]
        public string FilePath { get; set; }

        /// <summary>
        /// نوع الملف
        /// </summary>
        public string FileType { get; set; }

        /// <summary>
        /// حجم الملف بالبايت
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// وصف المرفق
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// تاريخ الإضافة
        /// </summary>
        public DateTime AddedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// التوثيق المرتبط
        /// </summary>
        public virtual MessageDocumentation MessageDocumentation { get; set; }
    }
}
