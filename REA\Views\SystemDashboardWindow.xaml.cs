using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using System.Windows;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// لوحة التحكم المتقدمة لمراقبة النظام
    /// </summary>
    public partial class SystemDashboardWindow : Window, INotifyPropertyChanged
    {
        private readonly Timer _updateTimer;
        private readonly ImportLogService _importLogService;
        private readonly AutoBackupService _backupService;
        
        private string _systemStatus = "🟢 متصل";
        private int _activeUsers = 1;
        private double _databaseSize = 0;
        private double _importSuccessRate = 0;
        private string _lastBackupTime = "غير متاح";
        private DateTime _lastUpdateTime = DateTime.Now;
        private double _memoryUsage = 0;
        private double _cpuUsage = 0;
        private int _activeProcesses = 0;
        private int _activeConnections = 1;

        public SystemDashboardWindow()
        {
            InitializeComponent();
            DataContext = this;
            
            _importLogService = new ImportLogService();
            _backupService = new AutoBackupService();
            
            SystemLogs = new ObservableCollection<SystemLogEntry>();
            SystemAlerts = new ObservableCollection<SystemAlert>();
            
            // إعداد مؤقت التحديث (كل 5 ثوان)
            _updateTimer = new Timer(5000);
            _updateTimer.Elapsed += OnUpdateTimerElapsed;
            _updateTimer.AutoReset = true;
            _updateTimer.Enabled = true;
            
            // تحميل البيانات الأولية
            _ = LoadInitialDataAsync();
            
            System.Diagnostics.Debug.WriteLine("🚀 تم فتح لوحة التحكم المتقدمة");
        }

        #region Properties

        public string SystemStatus
        {
            get => _systemStatus;
            set { _systemStatus = value; OnPropertyChanged(nameof(SystemStatus)); }
        }

        public int ActiveUsers
        {
            get => _activeUsers;
            set { _activeUsers = value; OnPropertyChanged(nameof(ActiveUsers)); }
        }

        public double DatabaseSize
        {
            get => _databaseSize;
            set { _databaseSize = value; OnPropertyChanged(nameof(DatabaseSize)); }
        }

        public double ImportSuccessRate
        {
            get => _importSuccessRate;
            set { _importSuccessRate = value; OnPropertyChanged(nameof(ImportSuccessRate)); }
        }

        public string LastBackupTime
        {
            get => _lastBackupTime;
            set { _lastBackupTime = value; OnPropertyChanged(nameof(LastBackupTime)); }
        }

        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set { _lastUpdateTime = value; OnPropertyChanged(nameof(LastUpdateTime)); }
        }

        public double MemoryUsage
        {
            get => _memoryUsage;
            set { _memoryUsage = value; OnPropertyChanged(nameof(MemoryUsage)); }
        }

        public double CpuUsage
        {
            get => _cpuUsage;
            set { _cpuUsage = value; OnPropertyChanged(nameof(CpuUsage)); }
        }

        public int ActiveProcesses
        {
            get => _activeProcesses;
            set { _activeProcesses = value; OnPropertyChanged(nameof(ActiveProcesses)); }
        }

        public int ActiveConnections
        {
            get => _activeConnections;
            set { _activeConnections = value; OnPropertyChanged(nameof(ActiveConnections)); }
        }

        public ObservableCollection<SystemLogEntry> SystemLogs { get; set; }
        public ObservableCollection<SystemAlert> SystemAlerts { get; set; }

        #endregion

        private async Task LoadInitialDataAsync()
        {
            try
            {
                // تحميل إحصائيات الاستيراد
                var importStats = await _importLogService.GetImportStatisticsAsync();
                ImportSuccessRate = importStats.SuccessRate;

                // حساب حجم قاعدة البيانات
                await UpdateDatabaseSizeAsync();

                // تحديث معلومات النسخ الاحتياطي
                UpdateBackupInfo();

                // إضافة سجلات النظام الأولية
                AddSystemLog("🚀", "تم تشغيل لوحة التحكم المتقدمة");
                AddSystemLog("📊", $"تم تحميل إحصائيات الاستيراد - معدل النجاح: {ImportSuccessRate:F1}%");
                AddSystemLog("💾", $"حجم قاعدة البيانات: {DatabaseSize:F1} MB");

                // إضافة تنبيهات النظام
                AddSystemAlert("ℹ️", "معلومات", "النظام يعمل بشكل طبيعي");
                
                if (ImportSuccessRate < 90)
                {
                    AddSystemAlert("⚠️", "تحذير", "معدل نجاح الاستيراد أقل من 90%");
                }
            }
            catch (Exception ex)
            {
                AddSystemLog("❌", $"خطأ في تحميل البيانات الأولية: {ex.Message}");
            }
        }

        private async void OnUpdateTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            try
            {
                await Dispatcher.InvokeAsync(async () =>
                {
                    await UpdateSystemMetricsAsync();
                    LastUpdateTime = DateTime.Now;
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث المقاييس: {ex.Message}");
            }
        }

        private async Task UpdateSystemMetricsAsync()
        {
            try
            {
                // تحديث استخدام الذاكرة والمعالج
                var currentProcess = Process.GetCurrentProcess();
                MemoryUsage = (currentProcess.WorkingSet64 / 1024.0 / 1024.0 / 16.0) * 100; // تقريبي
                CpuUsage = Environment.TickCount % 100; // محاكاة استخدام المعالج
                
                // تحديث العمليات النشطة
                ActiveProcesses = Process.GetProcesses().Length;
                
                // تحديث حجم قاعدة البيانات
                await UpdateDatabaseSizeAsync();
                
                // تحديث إحصائيات الاستيراد
                var importStats = await _importLogService.GetImportStatisticsAsync();
                ImportSuccessRate = importStats.SuccessRate;
                
                // إضافة سجل دوري
                if (DateTime.Now.Second % 30 == 0) // كل 30 ثانية
                {
                    AddSystemLog("🔄", "تحديث دوري للمقاييس");
                }
            }
            catch (Exception ex)
            {
                AddSystemLog("❌", $"خطأ في تحديث المقاييس: {ex.Message}");
            }
        }

        private async Task UpdateDatabaseSizeAsync()
        {
            try
            {
                var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SFDSystem");
                var dbPath = Path.Combine(appDataPath, "SFDDatabase.db");
                
                if (File.Exists(dbPath))
                {
                    var fileInfo = new FileInfo(dbPath);
                    DatabaseSize = fileInfo.Length / 1024.0 / 1024.0; // MB
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حساب حجم قاعدة البيانات: {ex.Message}");
            }
        }

        private void UpdateBackupInfo()
        {
            try
            {
                var backups = _backupService.GetAvailableBackups();
                if (backups.Length > 0)
                {
                    var lastBackup = backups[0];
                    var timeDiff = DateTime.Now - lastBackup.CreatedAt;
                    LastBackupTime = $"{timeDiff.TotalMinutes:F0}";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث معلومات النسخ الاحتياطي: {ex.Message}");
            }
        }

        private void AddSystemLog(string icon, string message)
        {
            Dispatcher.InvokeAsync(() =>
            {
                SystemLogs.Insert(0, new SystemLogEntry
                {
                    Timestamp = DateTime.Now,
                    Icon = icon,
                    Message = message
                });

                // الاحتفاظ بآخر 50 سجل فقط
                while (SystemLogs.Count > 50)
                {
                    SystemLogs.RemoveAt(SystemLogs.Count - 1);
                }
            });
        }

        private void AddSystemAlert(string icon, string title, string message)
        {
            Dispatcher.InvokeAsync(() =>
            {
                SystemAlerts.Insert(0, new SystemAlert
                {
                    Icon = icon,
                    Title = title,
                    Message = message,
                    Timestamp = DateTime.Now
                });

                // الاحتفاظ بآخر 10 تنبيهات فقط
                while (SystemAlerts.Count > 10)
                {
                    SystemAlerts.RemoveAt(SystemAlerts.Count - 1);
                }
            });
        }

        #region Button Events

        private async void CreateBackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddSystemLog("🔄", "بدء إنشاء نسخة احتياطية فورية...");
                var success = await _backupService.CreateBackupNowAsync();
                
                if (success)
                {
                    AddSystemLog("✅", "تم إنشاء النسخة الاحتياطية بنجاح");
                    UpdateBackupInfo();
                    MessageBox.Show("✅ تم إنشاء النسخة الاحتياطية بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    AddSystemLog("❌", "فشل في إنشاء النسخة الاحتياطية");
                    MessageBox.Show("❌ فشل في إنشاء النسخة الاحتياطية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                AddSystemLog("❌", $"خطأ في النسخ الاحتياطي: {ex.Message}");
            }
        }

        private void ShowImportStatsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var statsWindow = new ImportStatisticsWindow();
                statsWindow.ShowDialog();
                AddSystemLog("📊", "تم فتح نافذة إحصائيات الاستيراد");
            }
            catch (Exception ex)
            {
                AddSystemLog("❌", $"خطأ في فتح الإحصائيات: {ex.Message}");
            }
        }

        private void CleanTempFilesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تنظيف الملفات المؤقتة
                var tempPath = Path.GetTempPath();
                var tempFiles = Directory.GetFiles(tempPath, "SFD_*");
                
                foreach (var file in tempFiles)
                {
                    try
                    {
                        File.Delete(file);
                    }
                    catch { }
                }
                
                AddSystemLog("🗑️", $"تم تنظيف {tempFiles.Length} ملف مؤقت");
                MessageBox.Show($"✅ تم تنظيف {tempFiles.Length} ملف مؤقت", "تنظيف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                AddSystemLog("❌", $"خطأ في التنظيف: {ex.Message}");
            }
        }

        private void AdvancedSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            AddSystemLog("🔧", "فتح الإعدادات المتقدمة");
            MessageBox.Show("🔧 الإعدادات المتقدمة ستكون متاحة في التحديث القادم!", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "تصدير سجل النظام",
                    Filter = "ملفات النص (*.txt)|*.txt|جميع الملفات (*.*)|*.*",
                    FileName = $"SystemLog_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var logContent = string.Join("\n", SystemLogs.Select(log => $"{log.Timestamp:yyyy-MM-dd HH:mm:ss} {log.Icon} {log.Message}"));
                    File.WriteAllText(saveDialog.FileName, logContent);
                    
                    AddSystemLog("📋", "تم تصدير سجل النظام");
                    MessageBox.Show("✅ تم تصدير سجل النظام بنجاح!", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                AddSystemLog("❌", $"خطأ في التصدير: {ex.Message}");
            }
        }

        private async void FixDriverNamesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddSystemLog("🔧", "بدء تصحيح أسماء السائقين...");

                var result = MessageBox.Show(
                    "هل تريد تصحيح أسماء السائقين في قاعدة البيانات؟\n\nهذا سيقوم بتصحيح الأسماء المقطوعة وربطها بالأسماء الكاملة.",
                    "تصحيح أسماء السائقين",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // تشغيل أداة التصحيح
                    await DriverManagementSystem.Tools.FixDriverNamesDirectly.FixDriverNamesAsync();

                    // طباعة الإحصائيات
                    await DriverManagementSystem.Tools.FixDriverNamesDirectly.PrintDataStatisticsAsync();

                    AddSystemLog("✅", "تم تصحيح أسماء السائقين بنجاح");
                    MessageBox.Show("✅ تم تصحيح أسماء السائقين بنجاح!\n\nيرجى إعادة تشغيل التطبيق لرؤية التغييرات.",
                                  "تصحيح مكتمل",
                                  MessageBoxButton.OK,
                                  MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                AddSystemLog("❌", $"خطأ في تصحيح أسماء السائقين: {ex.Message}");
                MessageBox.Show($"❌ خطأ في تصحيح أسماء السائقين:\n{ex.Message}",
                              "خطأ",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
        }

        private async void RemoveDuplicateDriversButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddSystemLog("🔍", "فحص السائقين المكررين...");

                using var dataService = new DatabaseService();

                // أولاً فحص عدد السائقين الحالي
                var allDrivers = await dataService.GetDriversAsync();
                AddSystemLog("📊", $"العدد الحالي للسائقين: {allDrivers.Count}");

                var result = MessageBox.Show(
                    $"العدد الحالي للسائقين: {allDrivers.Count}\n\nهل تريد تشغيل عملية إزالة السائقين المكررين؟\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!",
                    "حذف السائقين المكررين",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    AddSystemLog("🗑️", "بدء حذف السائقين المكررين...");

                    // تشغيل أداة حذف السائقين المكررين
                    var removedCount = await dataService.RemoveDuplicateDriversAsync();

                    AddSystemLog("✅", $"تم حذف {removedCount} سائق مكرر");

                    // فحص العدد النهائي
                    var finalDrivers = await dataService.GetDriversAsync();
                    AddSystemLog("📊", $"العدد النهائي للسائقين: {finalDrivers.Count}");

                    if (removedCount > 0)
                    {
                        MessageBox.Show($"✅ تم حذف {removedCount} سائق مكرر بنجاح!\n\nالعدد النهائي: {finalDrivers.Count} سائق\n\nيرجى إعادة تشغيل التطبيق لرؤية التغييرات.",
                                      "حذف مكتمل",
                                      MessageBoxButton.OK,
                                      MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("✅ لا توجد سائقين مكررين للحذف!\n\nقاعدة البيانات نظيفة.",
                                      "فحص مكتمل",
                                      MessageBoxButton.OK,
                                      MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                AddSystemLog("❌", $"خطأ في حذف السائقين المكررين: {ex.Message}");
                MessageBox.Show($"❌ خطأ في حذف السائقين المكررين:\n{ex.Message}",
                              "خطأ",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
        }

        #endregion

        protected override void OnClosed(EventArgs e)
        {
            _updateTimer?.Stop();
            _updateTimer?.Dispose();
            _backupService?.Dispose();
            base.OnClosed(e);
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class SystemLogEntry
    {
        public DateTime Timestamp { get; set; }
        public string Icon { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public class SystemAlert
    {
        public string Icon { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
