﻿<Window x:Class="DriverManagementSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:DriverManagementSystem.Converters"
        Title="نظام إدارة الزيارات الميدانية - الصندوق الاجتماعي للتنمية"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
>

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Colors.xaml"/>
                <ResourceDictionary Source="Resources/Styles.xaml"/>
                <ResourceDictionary Source="Assets/SystemLogo.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <local:BoolToWidthConverter x:Key="BoolToWidthConverter"/>
            <local:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <local:BoolToToggleIconConverter x:Key="BoolToToggleIconConverter"/>
            <local:BoolToPaddingConverter x:Key="BoolToPaddingConverter"/>
            <local:BoolToAvatarSizeConverter x:Key="BoolToAvatarSizeConverter"/>
            <local:BoolToAvatarRadiusConverter x:Key="BoolToAvatarRadiusConverter"/>
            <local:BoolToAvatarFontSizeConverter x:Key="BoolToAvatarFontSizeConverter"/>
            <local:ActiveMenuItemConverter x:Key="ActiveMenuItemConverter"/>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="{Binding IsSidebarCollapsed, Converter={StaticResource BoolToWidthConverter}}"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Sidebar -->
        <Border Grid.Column="0"
                Background="#F8F9FA"
                BorderBrush="#E9ECEF"
                BorderThickness="0,0,1,0">
            <Border.Effect>
                <DropShadowEffect Color="#00000020" Direction="0" ShadowDepth="1" Opacity="0.1" BlurRadius="4"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- System Logo Section -->
                <Border Grid.Row="0"
                        Background="Transparent"
                        Padding="0">
                    <Grid Height="60">
                        <!-- Toggle Button -->
                        <Button HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                BorderThickness="0"
                                Foreground="#6C757D"
                                FontSize="16"
                                Padding="15"
                                Command="{Binding ToggleSidebarCommand}"
                                Cursor="Hand"
                                ToolTip="طي/توسيع القائمة الجانبية">
                            <TextBlock Text="☰" FontWeight="Normal"/>
                        </Button>
                    </Grid>
                </Border>



                <!-- Navigation Menu -->
                <ScrollViewer Grid.Row="1"
                            VerticalScrollBarVisibility="Hidden"
                            HorizontalScrollBarVisibility="Disabled"
                            Background="Transparent">
                    <StackPanel Margin="0,10,0,0">

                        <!-- 1. لوحة التحكم -->
                        <Button BorderThickness="0"
                                Command="{Binding NavigateCommand}"
                                CommandParameter="Dashboard"
                                Height="70"
                                Margin="8,4"
                                Cursor="Hand"
                                ToolTip="الانتقال إلى الصفحة الرئيسية ولوحة التحكم">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="{Binding SelectedMenuItem, Converter={StaticResource ActiveMenuItemConverter}, ConverterParameter=Dashboard}"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <Border Background="#2196F3"
                                        CornerRadius="12"
                                        Width="36" Height="36"
                                        Margin="0,8,0,8">
                                    <TextBlock Text="🏠" FontSize="18" Foreground="White"
                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="الرئيسية"
                                         FontSize="11"
                                         HorizontalAlignment="Center"
                                         TextAlignment="Center"
                                         Foreground="#333333"
                                         FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <!-- 2. إدارة المستخدمين -->
                        <Button BorderThickness="0"
                                Command="{Binding NavigateCommand}"
                                CommandParameter="UserManagement"
                                Height="70"
                                Margin="8,4"
                                Cursor="Hand"
                                ToolTip="إدارة المستخدمين وصلاحياتهم في النظام">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="{Binding SelectedMenuItem, Converter={StaticResource ActiveMenuItemConverter}, ConverterParameter=UserManagement}"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#F0F8FF"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <Border Background="#6C757D"
                                        CornerRadius="12"
                                        Width="36" Height="36"
                                        Margin="0,8,0,8">
                                    <TextBlock Text="👥" FontSize="18" Foreground="White"
                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="المستخدمين"
                                         FontSize="11"
                                         HorizontalAlignment="Center"
                                         TextAlignment="Center"
                                         Foreground="#333333"
                                         FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <!-- 3. بيانات النزول -->
                        <Button BorderThickness="0"
                                Command="{Binding NavigateCommand}"
                                CommandParameter="DropData"
                                Height="70"
                                Margin="8,4"
                                Cursor="Hand"
                                ToolTip="إنشاء زيارة ميدانية جديدة وإدخال بياناتها">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="{Binding SelectedMenuItem, Converter={StaticResource ActiveMenuItemConverter}, ConverterParameter=DropData}"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E8F5E8"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <Border Background="#6C757D"
                                        CornerRadius="12"
                                        Width="36" Height="36"
                                        Margin="0,8,0,8">
                                    <TextBlock Text="📊" FontSize="18" Foreground="White"
                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="جديد"
                                         FontSize="11"
                                         HorizontalAlignment="Center"
                                         TextAlignment="Center"
                                         Foreground="#333333"
                                         FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <!-- 4. سجل الزيارات الميدانية -->
                        <Button BorderThickness="0"
                                Command="{Binding NavigateCommand}"
                                CommandParameter="FieldVisitsLog"
                                Height="70"
                                Margin="8,4"
                                Cursor="Hand"
                                ToolTip="عرض وإدارة سجل الزيارات الميدانية المحفوظة">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="{Binding SelectedMenuItem, Converter={StaticResource ActiveMenuItemConverter}, ConverterParameter=FieldVisitsLog}"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#FFF3E0"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <Border Background="#6C757D"
                                        CornerRadius="12"
                                        Width="36" Height="36"
                                        Margin="0,8,0,8">
                                    <TextBlock Text="📅" FontSize="18" Foreground="White"
                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="البيانات"
                                         FontSize="11"
                                         HorizontalAlignment="Center"
                                         TextAlignment="Center"
                                         Foreground="#333333"
                                         FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <!-- 5. الوصول السريع -->
                        <Button BorderThickness="0"
                                Command="{Binding NavigateCommand}"
                                CommandParameter="QuickAccess"
                                Height="70"
                                Margin="8,4"
                                Cursor="Hand"
                                ToolTip="عرض السائقين المتواجدين بالميدان حالياً مع تفاصيل مهامهم">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="{Binding SelectedMenuItem, Converter={StaticResource ActiveMenuItemConverter}, ConverterParameter=QuickAccess}"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <Border Background="#2196F3"
                                        CornerRadius="12"
                                        Width="36" Height="36"
                                        Margin="0,8,0,8">
                                    <TextBlock Text="⚡" FontSize="18" Foreground="White"
                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="الوصول السريع"
                                         FontSize="11"
                                         HorizontalAlignment="Center"
                                         TextAlignment="Center"
                                         Foreground="#333333"
                                         FontWeight="Medium"/>
                            </StackPanel>
                        </Button>





                        <!-- 6. تقرير المهمة -->
                        <Button BorderThickness="0"
                                Command="{Binding NavigateCommand}"
                                CommandParameter="Statistics"
                                Height="70"
                                Margin="8,4"
                                Cursor="Hand"
                                ToolTip="عرض التقارير والإحصائيات الخاصة بالزيارات">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="{Binding SelectedMenuItem, Converter={StaticResource ActiveMenuItemConverter}, ConverterParameter=Statistics}"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#F3E5F5"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <Border Background="#6C757D"
                                        CornerRadius="12"
                                        Width="36" Height="36"
                                        Margin="0,8,0,8">
                                    <TextBlock Text="🎁" FontSize="18" Foreground="White"
                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="التقارير"
                                         FontSize="11"
                                         HorizontalAlignment="Center"
                                         TextAlignment="Center"
                                         Foreground="#333333"
                                         FontWeight="Medium"/>
                            </StackPanel>
                        </Button>



                        <!-- 7. إعداد قاعدة البيانات -->

                        <!-- 8. إضافة الضباط الجدد -->
                        <Button BorderThickness="0"
                                Click="AddOfficersButton_Click"
                                Height="70"
                                Margin="8,4"
                                Cursor="Hand"
                                ToolTip="إضافة الضباط الجدد إلى قاعدة البيانات">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E8F5E8"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <Border Background="#28A745"
                                        CornerRadius="12"
                                        Width="36" Height="36"
                                        Margin="0,8,0,8">
                                    <TextBlock Text="👥" FontSize="18" Foreground="White"
                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="إضافة الضباط"
                                         FontSize="11"
                                         HorizontalAlignment="Center"
                                         TextAlignment="Center"
                                         Foreground="#333333"
                                         FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <!-- 9. استعادة قاعدة البيانات -->

                    </StackPanel>
                </ScrollViewer>

                <!-- Test Acknowledgment Button -->

                <!-- Logout Button -->
                <Border Grid.Row="2"
                        BorderBrush="#E9ECEF"
                        BorderThickness="0,1,0,0"
                        Padding="0,20,0,20"
                        Background="Transparent" Margin="0,0,0,77">
                    <Button Background="Transparent"
                            BorderThickness="0"
                            Command="{Binding LogoutCommand}"
                            Height="70"
                            Margin="8,4"
                            Cursor="Hand"
                            ToolTip="تسجيل الخروج من النظام والعودة لشاشة تسجيل الدخول">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FFEBEE"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                        <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                            <Border Background="#6C757D"
                                    CornerRadius="12"
                                    Width="36" Height="36"
                                    Margin="0,8,0,8">
                                <TextBlock Text="🔒" FontSize="18" Foreground="White"
                                         HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="خروج"
                                     FontSize="11"
                                     HorizontalAlignment="Center"
                                     TextAlignment="Center"
                                     Foreground="#333333"
                                     FontWeight="Medium"/>
                        </StackPanel>
                    </Button>
                </Border>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <ContentPresenter x:Name="ContentArea" Content="{Binding CurrentView}"/>
            <!-- Hidden ReportView for printing access -->
            <ContentControl x:Name="ReportViewControl" Content="{Binding CurrentView}" Visibility="Collapsed"/>
        </Grid>
    </Grid>
</Window>
