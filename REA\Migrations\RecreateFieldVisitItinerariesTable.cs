using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using System;
using System.Threading.Tasks;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// إعادة إنشاء جدول خط السير الميدانية بالكامل - إصلاح نهائي
    /// </summary>
    public static class RecreateFieldVisitItinerariesTable
    {
        public static async Task ApplyAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء إعادة إنشاء جدول FieldVisitItineraries بالكامل...");

                // الخطوة 1: حذف الجدول بالكامل مع جميع القيود
                await context.Database.ExecuteSqlRawAsync(@"
                    -- حذف جميع القيود المرتبطة بالجدول
                    DECLARE @sql NVARCHAR(MAX) = '';
                    SELECT @sql = @sql + 'ALTER TABLE [' + TABLE_SCHEMA + '].[' + TABLE_NAME + '] DROP CONSTRAINT [' + CONSTRAINT_NAME + '];' + CHAR(13)
                    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
                    WHERE TABLE_NAME = 'FieldVisitItineraries' AND CONSTRAINT_TYPE = 'FOREIGN KEY';
                    EXEC sp_executesql @sql;

                    -- حذف الجدول إذا كان موجوداً
                    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'FieldVisitItineraries')
                    BEGIN
                        DROP TABLE [FieldVisitItineraries];
                        PRINT 'تم حذف الجدول القديم مع جميع القيود';
                    END
                ");

                System.Diagnostics.Debug.WriteLine("❌ تم حذف الجدول القديم");

                // الخطوة 2: إنشاء الجدول الجديد بالتعريف الصحيح
                var createTableSql = @"
                    CREATE TABLE [FieldVisitItineraries] (
                        [Id] int IDENTITY(1,1) NOT NULL,
                        [FieldVisitId] int NOT NULL,
                        [DayNumber] int NOT NULL,
                        [ItineraryText] nvarchar(1000) NOT NULL,
                        [CreatedAt] datetime2 NOT NULL DEFAULT (GETDATE()),
                        [UpdatedAt] datetime2 NULL,
                        [Notes] nvarchar(500) NULL,
                        [IsActive] bit NOT NULL DEFAULT (1),
                        CONSTRAINT [PK_FieldVisitItineraries] PRIMARY KEY ([Id])
                    );
                    PRINT 'تم إنشاء الجدول الجديد';
                ";

                await context.Database.ExecuteSqlRawAsync(createTableSql);
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء الجدول الجديد");

                // الخطوة 3: إضافة Foreign Key إذا كان جدول FieldVisits موجود
                var addForeignKeySql = @"
                    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'FieldVisits')
                    BEGIN
                        ALTER TABLE [FieldVisitItineraries] 
                        ADD CONSTRAINT [FK_FieldVisitItineraries_FieldVisits] 
                        FOREIGN KEY ([FieldVisitId]) REFERENCES [FieldVisits] ([Id]) ON DELETE CASCADE;
                        PRINT 'تم إضافة Foreign Key';
                    END
                ";

                await context.Database.ExecuteSqlRawAsync(addForeignKeySql);
                System.Diagnostics.Debug.WriteLine("🔗 تم إضافة Foreign Key");

                // الخطوة 4: إضافة الفهارس
                var addIndexesSql = @"
                    CREATE INDEX [IX_FieldVisitItineraries_FieldVisitId] ON [FieldVisitItineraries] ([FieldVisitId]);
                    CREATE INDEX [IX_FieldVisitItineraries_IsActive] ON [FieldVisitItineraries] ([IsActive]);
                    CREATE UNIQUE INDEX [IX_FieldVisitItineraries_FieldVisitId_DayNumber] ON [FieldVisitItineraries] ([FieldVisitId], [DayNumber]);
                    PRINT 'تم إضافة الفهارس';
                ";

                await context.Database.ExecuteSqlRawAsync(addIndexesSql);
                System.Diagnostics.Debug.WriteLine("📊 تم إضافة الفهارس");

                System.Diagnostics.Debug.WriteLine("✅ تم إعادة إنشاء جدول FieldVisitItineraries بنجاح!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة إنشاء الجدول: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود الجدول
        /// </summary>
        private static async Task<bool> CheckTableExistsAsync(ApplicationDbContext context)
        {
            try
            {
                var result = await context.Database.ExecuteSqlRawAsync(
                    "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'FieldVisitItineraries'"
                );
                return result > 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
