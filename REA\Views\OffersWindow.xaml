<Window x:Class="DriverManagementSystem.Views.OffersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:DriverManagementSystem.Views"
        Title="{Binding WindowTitle, FallbackValue='نظام إرسال الرسائل للسائقين - عروض الأسعار'}"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- المحولات -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- محول النص المشروط -->
        <local:BoolToTextConverter x:Key="BoolToTextConverter"/>

        <!-- محول الأيقونة المشروطة -->
        <local:BoolToIconConverter x:Key="BoolToIconConverter"/>

        <!-- محول حالة العرض إلى لون -->
        <local:OfferStatusToColorConverter x:Key="OfferStatusToColorConverter"/>

        <!-- تدرج الألوان للرأس -->
        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#6A5ACD" Offset="0"/>
            <GradientStop Color="#4169E1" Offset="1"/>
        </LinearGradientBrush>

        <!-- ستايل الأزرار -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <!-- ستايل الإحصائيات -->
        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- الرأس -->
        <Border Grid.Row="0" Background="{StaticResource HeaderGradient}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="{Binding IsEditMode, Converter={StaticResource BoolToIconConverter}, ConverterParameter='✏️🏆'}"
                                   FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding IsEditMode, Converter={StaticResource BoolToTextConverter}, ConverterParameter='تعديل عروض الأسعار|نظام عروض الأسعار للسائقين'}"
                                   FontSize="24" FontWeight="Bold"
                                   Foreground="White" Margin="0,0,0,5"/>
                        <Border Background="#FF6B35" CornerRadius="12" Padding="8,2" Margin="15,0,0,0"
                               Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TextBlock Text="وضع التعديل" FontSize="12" FontWeight="Bold" Foreground="White"/>
                        </Border>
                    </StackPanel>
                    <TextBlock Text="{Binding IsEditMode, Converter={StaticResource BoolToTextConverter}, ConverterParameter='تحرير وتعديل العروض المحفوظة للزيارة|إدارة واختيار أفضل العروض للزيارات الميدانية'}"
                               FontSize="14" Foreground="White" Opacity="0.9"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="🚚" FontSize="40" Foreground="White" Margin="10"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- معلومات الزيارة -->
        <Border Grid.Row="1" Background="White" Padding="15" Margin="10,10,10,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="رقم الزيارة:" FontWeight="Bold" 
                          VerticalAlignment="Center" Margin="0,0,10,0"/>
                
                <TextBox Grid.Column="1" Text="{Binding VisitNumber}"
                        ToolTip="أدخل رقم الزيارة"
                        BorderBrush="Gray" BorderThickness="1" Padding="5"/>

                <TextBlock Grid.Column="2" Text="عدد الأيام:" FontWeight="Bold"
                          VerticalAlignment="Center" Margin="20,0,10,0"/>

                <TextBox Grid.Column="3" Text="{Binding VisitDaysCount}"
                        ToolTip="عدد الأيام"
                        BorderBrush="Gray" BorderThickness="1" Padding="5"/>

                <Border Grid.Column="4" Background="#E8F5E8" CornerRadius="5" 
                       Padding="10" Margin="20,0,0,0" Visibility="{Binding HasWinner, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="{Binding WinnerDriver}" FontWeight="Bold" 
                              Foreground="#2E7D32" FontSize="14"/>
                </Border>
            </Grid>
        </Border>

        <!-- الإحصائيات -->
        <Border Grid.Row="2" Margin="10,5,10,0">
            <UniformGrid Rows="1" Columns="5">
                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="👥" FontSize="24" Foreground="#2196F3" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding TotalOffers}" FontSize="20" FontWeight="Bold"
                                  HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="إجمالي العروض" FontSize="12"
                                  HorizontalAlignment="Center" Foreground="Gray"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="✅" FontSize="24" Foreground="#4CAF50" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding SelectedOffers}" FontSize="20" FontWeight="Bold"
                                  HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="العروض المختارة" FontSize="12"
                                  HorizontalAlignment="Center" Foreground="Gray"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📉" FontSize="24" Foreground="#FF9800" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding LowestPrice, StringFormat='{}{0:N0} ريال'}" FontSize="16" FontWeight="Bold"
                                  HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="أقل سعر" FontSize="12"
                                  HorizontalAlignment="Center" Foreground="Gray"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📈" FontSize="24" Foreground="#F44336" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding HighestPrice, StringFormat='{}{0:N0} ريال'}" FontSize="16" FontWeight="Bold"
                                  HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="أعلى سعر" FontSize="12"
                                  HorizontalAlignment="Center" Foreground="Gray"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="🧮" FontSize="24" Foreground="#9C27B0" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding AveragePrice, StringFormat='{}{0:N0} ريال'}" FontSize="16" FontWeight="Bold"
                                  HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="متوسط السعر" FontSize="12"
                                  HorizontalAlignment="Center" Foreground="Gray"/>
                    </StackPanel>
                </Border>
            </UniformGrid>
        </Border>

        <!-- جدول العروض -->
        <Border Grid.Row="3" Background="White" Margin="10" CornerRadius="8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- أزرار التحكم -->
                <Border Grid.Row="0" Background="#F5F5F5" Padding="10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- أزرار التحكم الرئيسية -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Content="🔄 فلترة حسب السعر"
                                   Command="{Binding FilterByPriceCommand}"
                                   Style="{StaticResource ActionButtonStyle}"
                                   Background="#2196F3"/>

                            <Button Content="🏆 اعتماد السعر"
                                   Command="{Binding ApprovePriceCommand}"
                                   Style="{StaticResource ActionButtonStyle}"
                                   Background="#4CAF50"/>

                            <Button Content="💰 نقل الأسعار"
                                   Command="{Binding TransferSelectedDataCommand}"
                                   Style="{StaticResource ActionButtonStyle}"
                                   Background="#673AB7"/>

                            <Button Content="🗑️ إلغاء التحديد"
                                   Command="{Binding ClearSelectionCommand}"
                                   Style="{StaticResource ActionButtonStyle}"
                                   Background="#FF9800"/>

                            <Button Content="🔄 تحديث"
                                   Command="{Binding RefreshCommand}"
                                   Style="{StaticResource ActionButtonStyle}"
                                   Background="#9E9E9E"/>
                        </StackPanel>

                        <!-- مؤشر وضع التعديل -->
                        <Border Grid.Column="1" Background="#6C5CE7" CornerRadius="15" Padding="12,6"
                               Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="✏️" FontSize="14" Foreground="White" Margin="0,0,5,0"/>
                                <TextBlock Text="وضع التعديل نشط" FontSize="12" FontWeight="Bold" Foreground="White"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </Border>

                <!-- DataGrid -->
                <DataGrid Grid.Row="1" ItemsSource="{Binding DriverOffers}"
                         AutoGenerateColumns="False" CanUserAddRows="False"
                         CanUserDeleteRows="False" IsReadOnly="False"
                         GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                         AlternatingRowBackground="#F9F9F9" RowHeight="45"
                         FontSize="13" Margin="10"
                         CellEditEnding="OffersDataGrid_CellEditEnding">
                    
                    <DataGrid.Columns>
                        <!-- عمود الاختيار -->
                        <DataGridCheckBoxColumn Header="✔" Binding="{Binding IsSelected}" Width="50"/>
                        
                        <!-- عمود الفوز -->
                        <DataGridTextColumn Header="🏆" Binding="{Binding WinnerIndicator}" Width="50" IsReadOnly="True"/>
                        
                        <!-- اسم السائق -->
                        <DataGridTextColumn Header="اسم السائق" Binding="{Binding DriverName}" Width="150" IsReadOnly="True"/>
                        
                        <!-- رقم الهاتف -->
                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding PhoneNumber}" Width="120" IsReadOnly="True"/>
                        
                        <!-- نوع المركبة -->
                        <DataGridTextColumn Header="نوع المركبة" Binding="{Binding VehicleInfo}" Width="150" IsReadOnly="True"/>
                        
                        <!-- عدد الأيام -->
                        <DataGridTextColumn Header="عدد الأيام" Binding="{Binding DaysCount}" Width="80" IsReadOnly="True"/>
                        
                        <!-- المبلغ المقترح -->
                        <DataGridTextColumn Header="المبلغ المقترح (ريال)" Binding="{Binding ProposedAmount}" Width="150">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                    <Setter Property="Padding" Value="5,2"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                            <DataGridTextColumn.EditingElementStyle>
                                <Style TargetType="TextBox">
                                    <Setter Property="Background" Value="#BBDEFB"/>
                                    <Setter Property="Foreground" Value="#0D47A1"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.EditingElementStyle>
                        </DataGridTextColumn>
                        
                        <!-- إجمالي اليوم -->
                        <DataGridTextColumn Header="إجمالي اليوم" Binding="{Binding FormattedDailyRate}" Width="120" IsReadOnly="True"/>

                        <!-- عمود الحالة القابل للتعديل -->
                        <DataGridComboBoxColumn Header="الحالة" Width="130"
                                                SelectedItemBinding="{Binding OfferStatus, UpdateSourceTrigger=PropertyChanged}">
                            <DataGridComboBoxColumn.ElementStyle>
                                <Style TargetType="ComboBox">
                                    <Setter Property="ItemsSource">
                                        <Setter.Value>
                                            <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                                <sys:String>في الانتظار</sys:String>
                                                <sys:String>تم التقديم</sys:String>
                                                <sys:String>🏆 فائز</sys:String>
                                                <sys:String>✅ معتمد</sys:String>
                                                <sys:String>مرفوض</sys:String>
                                                <sys:String>قيد المراجعة</sys:String>
                                                <sys:String>ملغي</sys:String>
                                                <sys:String>😔 اعتذر</sys:String>
                                            </x:Array>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter Property="Background" Value="White"/>
                                    <Setter Property="BorderBrush" Value="#CCCCCC"/>
                                    <Setter Property="Padding" Value="5"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridComboBoxColumn.ElementStyle>
                            <DataGridComboBoxColumn.EditingElementStyle>
                                <Style TargetType="ComboBox">
                                    <Setter Property="ItemsSource">
                                        <Setter.Value>
                                            <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                                <sys:String>في الانتظار</sys:String>
                                                <sys:String>تم التقديم</sys:String>
                                                <sys:String>🏆 فائز</sys:String>
                                                <sys:String>✅ معتمد</sys:String>
                                                <sys:String>مرفوض</sys:String>
                                                <sys:String>قيد المراجعة</sys:String>
                                                <sys:String>ملغي</sys:String>
                                                <sys:String>😔 اعتذر</sys:String>
                                            </x:Array>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridComboBoxColumn.EditingElementStyle>
                        </DataGridComboBoxColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- أزرار الحفظ -->
        <Border Grid.Row="4" Background="#F5F5F5" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="فلترة أقل الأسعار" Command="{Binding FilterByPriceCommand}"
                        Style="{StaticResource ActionButtonStyle}" Background="#2196F3"
                        Width="150" Margin="0,0,10,0"
                        ToolTip="ترتيب العروض حسب السعر وتحديد أقل الأسعار تلقائياً"/>

                <Button Content="اعتماد الفائز" Command="{Binding ApproveWinnerCommand}"
                        Style="{StaticResource ActionButtonStyle}" Background="#4CAF50"
                        Width="130" Margin="0,0,10,0"
                        ToolTip="اعتماد السائق الذي حالته 'فائز' كسائق معتمد للزيارة"/>

                <Button Content="مسح التحديد" Command="{Binding ClearSelectionCommand}"
                        Style="{StaticResource ActionButtonStyle}" Background="#FF9800"
                        Width="120" Margin="0,0,10,0"/>

                <Button Content="{Binding IsEditMode, Converter={StaticResource BoolToTextConverter}, ConverterParameter='حفظ التعديلات|حفظ العروض'}"
                       Command="{Binding SaveCommand}"
                       Style="{StaticResource ActionButtonStyle}"
                       Background="{Binding IsEditMode, Converter={StaticResource OfferStatusToColorConverter}}"
                       Width="180" Margin="0,0,10,0"/>

                <Button Content="إغلاق"
                       Click="CloseButton_Click"
                       Style="{StaticResource ActionButtonStyle}"
                       Background="#F44336" Width="100"/>
            </StackPanel>
        </Border>

        <!-- مؤشر التحميل -->
        <Border Grid.RowSpan="5" Background="#80000000"
               Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Width="50" Height="50" IsIndeterminate="True"/>
                <TextBlock Text="جاري التحميل..." Foreground="White"
                          FontSize="16" Margin="0,10,0,0" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
