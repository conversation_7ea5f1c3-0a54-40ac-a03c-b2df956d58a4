using System;
using System.ComponentModel;

namespace DriverManagementSystem.Models
{
    public class Driver : INotifyPropertyChanged
    {
        private bool _isSelected;
        public int Id { get; set; }
        public string DriverCode { get; set; } = string.Empty; // كود السائق - يتولد آلياً

        // بيانات السائق
        public string Name { get; set; } = string.Empty; // اسم السائق
        public string PhoneNumber { get; set; } = string.Empty; // رقم التلفون
        public string CardNumber { get; set; } = string.Empty; // رقم البطاقة الشخصية
        public string CardType { get; set; } = string.Empty; // نوع البطاقة الشخصية
        public string CardIssuePlace { get; set; } = string.Empty; // مكان الإصدار للبطاقة
        public DateTime CardIssueDate { get; set; } // تاريخ اصدار البطاقة

        // بيانات السيارة
        public string VehicleNumber { get; set; } = string.Empty; // رقم السيارة
        public string VehicleType { get; set; } = string.Empty; // نوع السيارة
        public string VehicleColor { get; set; } = string.Empty; // لون السيارة
        public string VehicleModel { get; set; } = string.Empty; // موديل السيارة
        public string VehicleCapacity { get; set; } = string.Empty; // قدرة السيارة

        // بيانات الرخصة
        public string LicenseNumber { get; set; } = string.Empty; // رقم الرخصة
        public DateTime LicenseIssueDate { get; set; } // تاريخ الاصدار للرخصة

        // حقول إضافية
        public string Notes { get; set; } = string.Empty; // ملاحظات

        // حقول النظام
        public int SectorId { get; set; }
        public string SectorName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;

        // حالة السائق
        public string Status => IsActive ? "نشط" : "غير نشط";

        // حقول الأسعار والعروض
        public decimal? QuotedPrice { get; set; } // السعر المقدم من السائق
        public int? QuotedDays { get; set; } // عدد الأيام المقترحة
        public DateTime? QuoteDate { get; set; } // تاريخ تقديم العرض
        public string QuoteNotes { get; set; } = string.Empty; // ملاحظات العرض
        public bool HasQuote { get; set; } = false; // هل قدم عرض سعر

        // خاصية التحديد للواجهة
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        // تنفيذ INotifyPropertyChanged
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
