using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using Prism.Commands;
using Prism.Mvvm;

namespace DriverManagementSystem.ViewModels
{
    public class AddProjectViewModel : BindableBase, INotifyPropertyChanged
    {
        private readonly DataService _dataService;
        private string _projectNumber = string.Empty;
        private string _projectName = string.Empty;
        private string _projectNumberError = string.Empty;
        private string _projectNameError = string.Empty;
        private bool _hasProjectNumberError;
        private bool _hasProjectNameError;
        private string _searchText = string.Empty;
        private Project? _selectedProject;
        private bool _isEditMode;
        private ObservableCollection<Project> _projects = new();
        private ObservableCollection<Project> _filteredProjects = new();

        public AddProjectViewModel()
        {
            _dataService = new DataService();

            SaveProjectCommand = new DelegateCommand(SaveProject, CanSaveProject);
            UpdateProjectCommand = new DelegateCommand(UpdateProject, CanUpdateProject);
            ClearFormCommand = new DelegateCommand(ClearForm);
            EditProjectCommand = new DelegateCommand<Project>(EditProject);
            DeleteProjectCommand = new DelegateCommand<Project>(DeleteProject);
            RefreshProjectsCommand = new DelegateCommand(RefreshProjects);
            CancelCommand = new DelegateCommand(Cancel);

            // Subscribe to property changes to update validation
            PropertyChanged += OnPropertyChanged;

            // Load projects
            LoadProjects();
        }

        #region Properties

        public string ProjectNumber
        {
            get => _projectNumber;
            set
            {
                SetProperty(ref _projectNumber, value);
                ValidateProjectNumber();
                SaveProjectCommand.RaiseCanExecuteChanged();
            }
        }

        public string ProjectName
        {
            get => _projectName;
            set
            {
                SetProperty(ref _projectName, value);
                ValidateProjectName();
                SaveProjectCommand.RaiseCanExecuteChanged();
            }
        }

        public string ProjectNumberError
        {
            get => _projectNumberError;
            set => SetProperty(ref _projectNumberError, value);
        }

        public string ProjectNameError
        {
            get => _projectNameError;
            set => SetProperty(ref _projectNameError, value);
        }

        public bool HasProjectNumberError
        {
            get => _hasProjectNumberError;
            set => SetProperty(ref _hasProjectNumberError, value);
        }

        public bool HasProjectNameError
        {
            get => _hasProjectNameError;
            set => SetProperty(ref _hasProjectNameError, value);
        }

        public bool CanSave => CanSaveProject();

        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                FilterProjects();
            }
        }

        public Project? SelectedProject
        {
            get => _selectedProject;
            set => SetProperty(ref _selectedProject, value);
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }

        public bool CanUpdate => CanUpdateProject();

        public ObservableCollection<Project> Projects
        {
            get => _projects;
            set => SetProperty(ref _projects, value);
        }

        public ObservableCollection<Project> FilteredProjects
        {
            get => _filteredProjects;
            set => SetProperty(ref _filteredProjects, value);
        }

        public int ProjectsCount => FilteredProjects?.Count ?? 0;

        #endregion

        #region Commands

        public DelegateCommand SaveProjectCommand { get; }
        public DelegateCommand UpdateProjectCommand { get; }
        public DelegateCommand ClearFormCommand { get; }
        public DelegateCommand<Project> EditProjectCommand { get; }
        public DelegateCommand<Project> DeleteProjectCommand { get; }
        public DelegateCommand RefreshProjectsCommand { get; }
        public DelegateCommand CancelCommand { get; }

        #endregion

        #region Command Implementations

        private async void SaveProject()
        {
            try
            {
                // Final validation
                if (!ValidateAll())
                {
                    MessageBox.Show("يرجى تصحيح الأخطاء قبل الحفظ", "خطأ في البيانات", 
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Check for duplicate project number
                var existingProjects = await _dataService.GetProjectsAsync();
                if (existingProjects.Any(p => p.ProjectNumber.Equals(ProjectNumber, StringComparison.OrdinalIgnoreCase)))
                {
                    ProjectNumberError = "رقم المشروع موجود مسبقاً";
                    HasProjectNumberError = true;
                    MessageBox.Show("رقم المشروع موجود مسبقاً، يرجى استخدام رقم آخر", "رقم مكرر", 
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Create new project
                var newProject = new Project
                {
                    ProjectNumber = ProjectNumber.Trim(),
                    ProjectName = ProjectName.Trim()
                };

                // Save project
                var success = await _dataService.AddProjectAsync(newProject);
                
                if (success)
                {
                    MessageBox.Show("✅ تم حفظ المشروع بنجاح!", "نجح الحفظ",
                                   MessageBoxButton.OK, MessageBoxImage.Information);

                    // Refresh projects list and clear form
                    await LoadProjects();
                    ClearForm();
                }
                else
                {
                    MessageBox.Show("❌ فشل في حفظ المشروع", "خطأ",
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في حفظ المشروع: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool CanSaveProject()
        {
            return !string.IsNullOrWhiteSpace(ProjectNumber) &&
                   !string.IsNullOrWhiteSpace(ProjectName) &&
                   !HasProjectNumberError &&
                   !HasProjectNameError;
        }

        private async void UpdateProject()
        {
            try
            {
                if (!ValidateAll())
                {
                    MessageBox.Show("يرجى تصحيح الأخطاء قبل التحديث", "خطأ في البيانات",
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (SelectedProject == null)
                {
                    MessageBox.Show("لم يتم اختيار مشروع للتحديث", "خطأ",
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Check for duplicate project number (excluding current project)
                var existingProjects = await _dataService.GetProjectsAsync();
                if (existingProjects.Any(p => p.ProjectNumber.Equals(ProjectNumber, StringComparison.OrdinalIgnoreCase) && p.Id != SelectedProject.Id))
                {
                    ProjectNumberError = "رقم المشروع موجود مسبقاً";
                    HasProjectNumberError = true;
                    MessageBox.Show("رقم المشروع موجود مسبقاً، يرجى استخدام رقم آخر", "رقم مكرر",
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Update project
                SelectedProject.ProjectNumber = ProjectNumber.Trim();
                SelectedProject.ProjectName = ProjectName.Trim();

                var success = await _dataService.UpdateProjectAsync(SelectedProject);

                if (success)
                {
                    MessageBox.Show("✅ تم تحديث المشروع بنجاح!", "نجح التحديث",
                                   MessageBoxButton.OK, MessageBoxImage.Information);

                    // Refresh projects list and clear form
                    await LoadProjects();
                    ClearForm();
                }
                else
                {
                    MessageBox.Show("❌ فشل في تحديث المشروع", "خطأ",
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تحديث المشروع: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool CanUpdateProject()
        {
            return IsEditMode && SelectedProject != null &&
                   !string.IsNullOrWhiteSpace(ProjectNumber) &&
                   !string.IsNullOrWhiteSpace(ProjectName) &&
                   !HasProjectNumberError &&
                   !HasProjectNameError;
        }

        private void ClearForm()
        {
            ProjectNumber = string.Empty;
            ProjectName = string.Empty;
            SelectedProject = null;
            IsEditMode = false;
            ProjectNumberError = string.Empty;
            ProjectNameError = string.Empty;
            HasProjectNumberError = false;
            HasProjectNameError = false;
        }

        private void EditProject(Project? project)
        {
            if (project != null)
            {
                SelectedProject = project;
                ProjectNumber = project.ProjectNumber;
                ProjectName = project.ProjectName;
                IsEditMode = true;
            }
        }

        private async void DeleteProject(Project? project)
        {
            if (project == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من حذف المشروع:\n{project.ProjectNumber} - {project.ProjectName}?",
                                       "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var success = await _dataService.DeleteProjectAsync(project.Id);
                    if (success)
                    {
                        MessageBox.Show("✅ تم حذف المشروع بنجاح!", "نجح الحذف",
                                       MessageBoxButton.OK, MessageBoxImage.Information);

                        // Refresh projects list and clear form if deleted project was selected
                        await LoadProjects();
                        if (SelectedProject?.Id == project.Id)
                        {
                            ClearForm();
                        }
                    }
                    else
                    {
                        MessageBox.Show("❌ فشل في حذف المشروع", "خطأ",
                                       MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"❌ خطأ في حذف المشروع: {ex.Message}", "خطأ",
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void RefreshProjects()
        {
            await LoadProjects();
        }

        private void Cancel()
        {
            CloseWindow(false);
        }

        #endregion

        #region Validation

        private void ValidateProjectNumber()
        {
            if (string.IsNullOrWhiteSpace(ProjectNumber))
            {
                ProjectNumberError = "رقم المشروع مطلوب";
                HasProjectNumberError = true;
            }
            else if (ProjectNumber.Trim().Length < 3)
            {
                ProjectNumberError = "رقم المشروع قصير جداً";
                HasProjectNumberError = true;
            }
            else
            {
                ProjectNumberError = string.Empty;
                HasProjectNumberError = false;
            }
        }

        private void ValidateProjectName()
        {
            if (string.IsNullOrWhiteSpace(ProjectName))
            {
                ProjectNameError = "اسم المشروع مطلوب";
                HasProjectNameError = true;
            }
            else if (ProjectName.Trim().Length < 10)
            {
                ProjectNameError = "اسم المشروع قصير جداً (10 أحرف على الأقل)";
                HasProjectNameError = true;
            }
            else
            {
                ProjectNameError = string.Empty;
                HasProjectNameError = false;
            }
        }

        private bool ValidateAll()
        {
            ValidateProjectNumber();
            ValidateProjectName();
            return !HasProjectNumberError && !HasProjectNameError;
        }

        #endregion

        #region Helper Methods

        private async Task LoadProjects()
        {
            try
            {
                // Force refresh data from files first
                await _dataService.RefreshAllDataAsync();

                var projects = await _dataService.GetProjectsAsync();
                Projects.Clear();
                foreach (var project in projects)
                {
                    Projects.Add(project);
                }
                FilterProjects();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المشاريع: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FilterProjects()
        {
            FilteredProjects.Clear();

            var filtered = string.IsNullOrWhiteSpace(SearchText)
                ? Projects
                : Projects.Where(p =>
                    p.ProjectNumber.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    p.ProjectName.Contains(SearchText, StringComparison.OrdinalIgnoreCase));

            foreach (var project in filtered)
            {
                FilteredProjects.Add(project);
            }

            RaisePropertyChanged(nameof(ProjectsCount));
        }

        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(HasProjectNumberError) ||
                e.PropertyName == nameof(HasProjectNameError))
            {
                RaisePropertyChanged(nameof(CanSave));
                RaisePropertyChanged(nameof(CanUpdate));
            }
        }

        private void CloseWindow(bool dialogResult)
        {
            // Find the window and close it
            foreach (Window window in Application.Current.Windows)
            {
                if (window.DataContext == this)
                {
                    try
                    {
                        window.DialogResult = dialogResult;
                    }
                    catch
                    {
                        // If setting DialogResult fails, just close the window
                    }
                    window.Close();
                    break;
                }
            }
        }

        #endregion
    }
}
