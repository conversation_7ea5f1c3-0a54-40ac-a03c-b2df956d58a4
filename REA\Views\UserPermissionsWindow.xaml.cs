using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class UserPermissionsWindow : Window
    {
        private readonly IUserService _userService;
        private readonly User _user;
        private readonly List<CheckBox> _permissionCheckBoxes = new List<CheckBox>();

        public UserPermissionsWindow(IUserService userService, User user)
        {
            InitializeComponent();
            _userService = userService;
            _user = user;
            LoadUserPermissions();
        }

        private async void LoadUserPermissions()
        {
            UserInfoText.Text = $"المستخدم: {_user.FullName} (@{_user.Username})";

            try
            {
                var userPermissions = await _userService.GetUserPermissionsAsync(_user.UserId);
                CreatePermissionControls(userPermissions);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصلاحيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreatePermissionControls(List<UserPermission> userPermissions)
        {
            PermissionsPanel.Children.Clear();
            _permissionCheckBoxes.Clear();

            var allPermissions = GetAllAvailablePermissions();

            foreach (var permission in allPermissions)
            {
                var existingPermission = userPermissions.FirstOrDefault(p => p.PermissionName == permission.Key);
                var isGranted = existingPermission?.IsGranted ?? false;

                var permissionCard = CreatePermissionCard(permission.Key, permission.Value, isGranted);
                PermissionsPanel.Children.Add(permissionCard);
            }
        }

        private Border CreatePermissionCard(string permissionName, string permissionDescription, bool isGranted)
        {
            var card = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
                BorderThickness = new Thickness(1, 1, 1, 1),
                CornerRadius = new CornerRadius(6),
                Padding = new Thickness(15, 15, 15, 15),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            var checkBox = new CheckBox
            {
                IsChecked = isGranted,
                VerticalAlignment = VerticalAlignment.Center,
                Tag = permissionName
            };
            Grid.SetColumn(checkBox, 0);
            _permissionCheckBoxes.Add(checkBox);

            var textPanel = new StackPanel
            {
                Margin = new Thickness(15, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(textPanel, 1);

            textPanel.Children.Add(new TextBlock
            {
                Text = permissionDescription,
                FontWeight = FontWeights.Bold,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(51, 51, 51))
            });

            textPanel.Children.Add(new TextBlock
            {
                Text = GetPermissionIcon(permissionName) + " " + permissionName,
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                Margin = new Thickness(0, 2, 0, 0)
            });

            grid.Children.Add(checkBox);
            grid.Children.Add(textPanel);
            card.Child = grid;

            return card;
        }

        private Dictionary<string, string> GetAllAvailablePermissions()
        {
            return new Dictionary<string, string>
            {
                { "ViewDashboard", "عرض لوحة التحكم" },
                { "ManageDrivers", "إدارة السائقين" },
                { "ManageUsers", "إدارة المستخدمين" },
                { "ViewReports", "عرض التقارير" },
                { "ManageSettings", "إدارة الإعدادات" },
                { "ManageContracts", "إدارة العقود" },
                { "ManagePricing", "إدارة الأسعار" },
                { "SendMessages", "إرسال الرسائل" },
                { "ViewRoutes", "عرض الطرق" },
                { "ManageDropData", "إدارة بيانات النزول" }
            };
        }

        private string GetPermissionIcon(string permissionName)
        {
            return permissionName switch
            {
                "ViewDashboard" => "🏠",
                "ManageDrivers" => "👥",
                "ManageUsers" => "👤",
                "ViewReports" => "📊",
                "ManageSettings" => "⚙️",
                "ManageContracts" => "📄",
                "ManagePricing" => "💰",
                "SendMessages" => "📧",
                "ViewRoutes" => "🗺️",
                "ManageDropData" => "📍",
                _ => "🔧"
            };
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show loading state
                SaveButton.IsEnabled = false;
                SaveButton.Content = "⏳ جاري الحفظ...";
                SelectAllButton.IsEnabled = false;
                ClearAllButton.IsEnabled = false;
                CancelButton.IsEnabled = false;

                var permissions = new List<UserPermission>();

                foreach (var checkBox in _permissionCheckBoxes)
                {
                    var permissionName = checkBox.Tag.ToString();
                    var allPermissions = GetAllAvailablePermissions();
                    var permissionDescription = allPermissions.GetValueOrDefault(permissionName ?? "", "");

                    permissions.Add(new UserPermission
                    {
                        UserId = _user.UserId,
                        PermissionName = permissionName ?? "",
                        PermissionDescription = permissionDescription,
                        IsGranted = checkBox.IsChecked ?? false,
                        CreatedBy = "admin" // Should be current user
                    });
                }

                var success = await _userService.UpdateUserPermissionsAsync(_user.UserId, permissions);

                if (success)
                {
                    MessageBox.Show("تم حفظ الصلاحيات بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الصلاحيات!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الصلاحيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // Restore button states
                SaveButton.IsEnabled = true;
                SaveButton.Content = "💾 حفظ الصلاحيات";
                SelectAllButton.IsEnabled = true;
                ClearAllButton.IsEnabled = true;
                CancelButton.IsEnabled = true;
            }
        }

        private void SelectAllButton_Click(object sender, RoutedEventArgs e)
        {
            foreach (var checkBox in _permissionCheckBoxes)
            {
                checkBox.IsChecked = true;
            }
        }

        private void ClearAllButton_Click(object sender, RoutedEventArgs e)
        {
            foreach (var checkBox in _permissionCheckBoxes)
            {
                checkBox.IsChecked = false;
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
