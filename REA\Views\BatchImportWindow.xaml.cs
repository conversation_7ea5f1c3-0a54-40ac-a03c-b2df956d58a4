using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة الاستيراد المتعدد من Excel
    /// </summary>
    public partial class BatchImportWindow : Window, INotifyPropertyChanged
    {
        private readonly BatchExcelImportService _batchImportService;
        private readonly FolderMemoryService _folderMemoryService;
        private CancellationTokenSource? _cancellationTokenSource;
        
        private ObservableCollection<ExcelFileInfo> _selectedFiles = new();
        private bool _isImporting = false;
        private double _progressPercentage = 0;
        private string _progressText = "";
        private int _successfulFiles = 0;
        private int _failedFiles = 0;
        private int _remainingFiles = 0;
        private BatchImportResult? _lastResult;

        public BatchImportWindow()
        {
            InitializeComponent();
            DataContext = this;
            
            _batchImportService = new BatchExcelImportService();
            _folderMemoryService = new FolderMemoryService();
            
            System.Diagnostics.Debug.WriteLine("📂 تم فتح نافذة الاستيراد المتعدد");
        }

        #region Properties

        public ObservableCollection<ExcelFileInfo> SelectedFiles
        {
            get => _selectedFiles;
            set { _selectedFiles = value; OnPropertyChanged(nameof(SelectedFiles)); }
        }

        public int SelectedFilesCount => SelectedFiles.Count;

        public bool IsImporting
        {
            get => _isImporting;
            set 
            { 
                _isImporting = value; 
                OnPropertyChanged(nameof(IsImporting));
                OnPropertyChanged(nameof(CanStartImport));
                OnPropertyChanged(nameof(CanStopImport));
            }
        }

        public double ProgressPercentage
        {
            get => _progressPercentage;
            set { _progressPercentage = value; OnPropertyChanged(nameof(ProgressPercentage)); }
        }

        public string ProgressText
        {
            get => _progressText;
            set { _progressText = value; OnPropertyChanged(nameof(ProgressText)); }
        }

        public int SuccessfulFiles
        {
            get => _successfulFiles;
            set { _successfulFiles = value; OnPropertyChanged(nameof(SuccessfulFiles)); }
        }

        public int FailedFiles
        {
            get => _failedFiles;
            set { _failedFiles = value; OnPropertyChanged(nameof(FailedFiles)); }
        }

        public int RemainingFiles
        {
            get => _remainingFiles;
            set { _remainingFiles = value; OnPropertyChanged(nameof(RemainingFiles)); }
        }

        public bool CanStartImport => !IsImporting && SelectedFiles.Count > 0;
        public bool CanStopImport => IsImporting;
        public bool CanViewResults => !IsImporting && _lastResult != null;

        public Visibility ProgressVisibility => IsImporting ? Visibility.Visible : Visibility.Collapsed;

        #endregion

        #region Button Events

        private void SelectFilesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختر ملفات Excel للاستيراد",
                    Filter = "ملفات Excel (*.xlsx;*.xls)|*.xlsx;*.xls|جميع الملفات (*.*)|*.*",
                    Multiselect = true,
                    InitialDirectory = _folderMemoryService.GetLastFolder()
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    foreach (var filePath in openFileDialog.FileNames)
                    {
                        AddFileToList(filePath);
                    }

                    // حفظ آخر مجلد
                    if (openFileDialog.FileNames.Length > 0)
                    {
                        var folder = _folderMemoryService.GetFolderFromPath(openFileDialog.FileNames[0]);
                        _folderMemoryService.SaveLastFolder(folder);
                    }

                    OnPropertyChanged(nameof(SelectedFilesCount));
                    OnPropertyChanged(nameof(CanStartImport));
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار الملفات:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SelectFolderButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // استخدام OpenFileDialog بدلاً من FolderBrowserDialog
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختر ملفات Excel من المجلد",
                    Filter = "ملفات Excel (*.xlsx;*.xls)|*.xlsx;*.xls|جميع الملفات (*.*)|*.*",
                    Multiselect = true,
                    InitialDirectory = _folderMemoryService.GetLastFolder()
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    foreach (var filePath in openFileDialog.FileNames)
                    {
                        AddFileToList(filePath);
                    }

                    // حفظ آخر مجلد
                    if (openFileDialog.FileNames.Length > 0)
                    {
                        var folder = _folderMemoryService.GetFolderFromPath(openFileDialog.FileNames[0]);
                        _folderMemoryService.SaveLastFolder(folder);
                    }

                    OnPropertyChanged(nameof(SelectedFilesCount));
                    OnPropertyChanged(nameof(CanStartImport));

                    MessageBox.Show($"تم إضافة {openFileDialog.FileNames.Length} ملف Excel", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار الملفات:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearListButton_Click(object sender, RoutedEventArgs e)
        {
            SelectedFiles.Clear();
            OnPropertyChanged(nameof(SelectedFilesCount));
            OnPropertyChanged(nameof(CanStartImport));
        }

        private async void StartImportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SelectedFiles.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار ملفات للاستيراد أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                IsImporting = true;
                _cancellationTokenSource = new CancellationTokenSource();

                // إعداد خيارات الاستيراد
                var options = new BatchImportOptions
                {
                    DefaultIndexValue = DefaultIndexTextBox.Text.Trim(),
                    IndexPattern = IndexPatternTextBox.Text.Trim(),
                    DelayBetweenFiles = int.TryParse(DelayTextBox.Text, out int delay) ? delay : 500,
                    StopOnFirstError = StopOnErrorCheckBox.IsChecked == true
                };

                // بدء الاستيراد
                var filePaths = SelectedFiles.Select(f => f.FilePath).ToArray();
                _lastResult = await _batchImportService.ImportMultipleFilesAsync(filePaths, options);

                // عرض النتائج
                if (_lastResult.Success)
                {
                    MessageBox.Show($"✅ تم الاستيراد بنجاح!\n\n" +
                                  $"نجح: {_lastResult.SuccessfulFiles}\n" +
                                  $"فشل: {_lastResult.FailedFiles}\n" +
                                  $"معدل النجاح: {_lastResult.SuccessRate:F1}%\n" +
                                  $"الوقت المستغرق: {_lastResult.ProcessingTime.TotalSeconds:F1} ثانية",
                                  "اكتمل الاستيراد", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"❌ فشل الاستيراد:\n{_lastResult.ErrorMessage}", 
                                  "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عملية الاستيراد:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsImporting = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
                OnPropertyChanged(nameof(CanViewResults));
            }
        }

        private void StopImportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _cancellationTokenSource?.Cancel();
                MessageBox.Show("تم إيقاف عملية الاستيراد", "إيقاف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إيقاف الاستيراد:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewResultsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_lastResult != null)
                {
                    var resultsWindow = new BatchImportResultsWindow(_lastResult);
                    resultsWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض النتائج:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            if (IsImporting)
            {
                var result = MessageBox.Show("عملية الاستيراد جارية. هل تريد إيقافها والإغلاق؟", 
                                           "تأكيد الإغلاق", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    _cancellationTokenSource?.Cancel();
                    Close();
                }
            }
            else
            {
                Close();
            }
        }

        #endregion

        private void AddFileToList(string filePath)
        {
            try
            {
                // تجنب الملفات المكررة
                if (SelectedFiles.Any(f => f.FilePath.Equals(filePath, StringComparison.OrdinalIgnoreCase)))
                    return;

                var fileInfo = new FileInfo(filePath);
                var excelFileInfo = new ExcelFileInfo
                {
                    FileName = fileInfo.Name,
                    FilePath = filePath,
                    FileSize = FormatFileSize(fileInfo.Length),
                    LastModified = fileInfo.LastWriteTime
                };

                SelectedFiles.Add(excelFileInfo);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة الملف {filePath}: {ex.Message}");
            }
        }

        private static string FormatFileSize(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return $"{number:n1} {suffixes[counter]}";
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ExcelFileInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FileSize { get; set; } = string.Empty;
        public DateTime LastModified { get; set; }
    }
}
