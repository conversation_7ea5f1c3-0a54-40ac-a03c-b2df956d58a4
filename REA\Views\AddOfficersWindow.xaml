<Window x:Class="DriverManagementSystem.Views.AddOfficersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة الضباط الجدد" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="#F8F9FA"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButtonStyle" BasedOn="{StaticResource ModernButtonStyle}" TargetType="Button">
            <Setter Property="Background" Value="#28A745"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#218838"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Danger Button Style -->
        <Style x:Key="DangerButtonStyle" BasedOn="{StaticResource ModernButtonStyle}" TargetType="Button">
            <Setter Property="Background" Value="#DC3545"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#C82333"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20"
               BorderBrush="#DEE2E6" BorderThickness="1">
            <StackPanel>
                <TextBlock Text="🏢 إضافة الضباط الجدد إلى قاعدة البيانات"
                         FontSize="20" FontWeight="Bold" Foreground="#2C3E50"
                         HorizontalAlignment="Center" Margin="0,0,0,10"/>
                
                <TextBlock Text="سيتم إضافة جميع الضباط الجدد مع التحقق من عدم التكرار"
                         FontSize="14" Foreground="#6C757D"
                         HorizontalAlignment="Center"/>
                
                <!-- Statistics -->
                <Grid Margin="0,15,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="6" Padding="10" Margin="0,0,5,0">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="📊 إجمالي الضباط" FontWeight="Bold" FontSize="12" Foreground="#1976D2"/>
                            <TextBlock x:Name="TotalOfficersText" Text="37" FontSize="18" FontWeight="Bold" Foreground="#1976D2"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Grid.Column="1" Background="#E8F5E8" CornerRadius="6" Padding="10" Margin="5,0">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="✅ تم إضافتها" FontWeight="Bold" FontSize="12" Foreground="#2E7D32"/>
                            <TextBlock x:Name="AddedOfficersText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#2E7D32"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Grid.Column="2" Background="#FFF3E0" CornerRadius="6" Padding="10" Margin="5,0,0,0">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="⚠️ تم تخطيها" FontWeight="Bold" FontSize="12" Foreground="#F57C00"/>
                            <TextBlock x:Name="SkippedOfficersText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#F57C00"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Progress and Log -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20"
               BorderBrush="#DEE2E6" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Progress Bar -->
                <StackPanel Grid.Row="0" Margin="0,0,0,15">
                    <TextBlock Text="التقدم:" FontWeight="Bold" FontSize="14" Foreground="#2C3E50" Margin="0,0,0,5"/>
                    <ProgressBar x:Name="ProgressBar" Height="20" Background="#F8F9FA" 
                               Foreground="#007ACC" BorderBrush="#DEE2E6" BorderThickness="1"/>
                    <TextBlock x:Name="ProgressText" Text="جاهز للبدء..." FontSize="12" 
                             Foreground="#6C757D" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Status -->
                <Border Grid.Row="1" x:Name="StatusBorder" Background="#F8F9FA" CornerRadius="6" 
                       Padding="15" Margin="0,0,0,15" Visibility="Collapsed">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock x:Name="StatusIcon" Text="ℹ️" FontSize="16" Margin="0,0,10,0"/>
                        <TextBlock x:Name="StatusText" Text="" FontWeight="SemiBold" FontSize="14"/>
                    </StackPanel>
                </Border>

                <!-- Log -->
                <StackPanel Grid.Row="2">
                    <TextBlock Text="سجل العمليات:" FontWeight="Bold" FontSize="14" Foreground="#2C3E50" Margin="0,0,0,10"/>
                    <Border Background="#F8F9FA" CornerRadius="6" BorderBrush="#DEE2E6" BorderThickness="1">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="250">
                            <TextBlock x:Name="LogTextBlock" Text="انتظار بدء العملية..."
                                     Padding="15" FontFamily="Consolas" FontSize="12"
                                     Foreground="#495057" TextWrapping="Wrap"/>
                        </ScrollViewer>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="StartButton" Content="🚀 بدء إضافة الضباط" 
                   Style="{StaticResource SuccessButtonStyle}"
                   Click="StartButton_Click"/>
            
            <Button x:Name="ViewOfficersButton" Content="👥 عرض الضباط" 
                   Style="{StaticResource ModernButtonStyle}"
                   Click="ViewOfficersButton_Click"
                   IsEnabled="False"/>
            
            <Button Content="❌ إغلاق" 
                   Style="{StaticResource DangerButtonStyle}"
                   Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
