using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.Models;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة عروض الأسعار للسائقين
    /// </summary>
    public partial class OffersWindow : Window
    {
        private OffersViewModel _viewModel;

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public OffersWindow()
        {
            InitializeComponent();
            InitializeViewModel();
        }

        /// <summary>
        /// منشئ مع قائمة السائقين المحددين
        /// </summary>
        /// <param name="selectedDrivers">قائمة السائقين المحددين</param>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="daysCount">عدد الأيام</param>
        public OffersWindow(ObservableCollection<Driver> selectedDrivers, string visitNumber = "", int daysCount = 1)
        {
            InitializeComponent();
            InitializeViewModel();
            
            // تحديد معلومات الزيارة
            _viewModel.VisitNumber = visitNumber;
            _viewModel.VisitDaysCount = daysCount;
            
            // تحميل السائقين المحددين
            _ = _viewModel.LoadDriverOffers(selectedDrivers);
        }

        /// <summary>
        /// منشئ مع خدمة البيانات المخصصة
        /// </summary>
        /// <param name="dataService">خدمة البيانات</param>
        public OffersWindow(IDataService dataService)
        {
            InitializeComponent();
            InitializeViewModel(dataService);
        }

        /// <summary>
        /// منشئ شامل
        /// </summary>
        /// <param name="selectedDrivers">قائمة السائقين المحددين</param>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="daysCount">عدد الأيام</param>
        /// <param name="dataService">خدمة البيانات</param>
        public OffersWindow(ObservableCollection<Driver> selectedDrivers, string visitNumber, int daysCount, IDataService dataService)
        {
            InitializeComponent();
            InitializeViewModel(dataService);

            // تحديد معلومات الزيارة
            _viewModel.VisitNumber = visitNumber;
            _viewModel.VisitDaysCount = daysCount;

            // تحميل السائقين المحددين
            _ = _viewModel.LoadDriverOffers(selectedDrivers);
        }

        /// <summary>
        /// منشئ لوضع التعديل - تحميل العروض المحفوظة
        /// </summary>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="daysCount">عدد الأيام</param>
        /// <param name="isEditMode">وضع التعديل</param>
        public OffersWindow(string visitNumber, int daysCount, bool isEditMode = true)
        {
            InitializeComponent();
            InitializeViewModel();

            // تحديد معلومات الزيارة
            _viewModel.VisitNumber = visitNumber;
            _viewModel.VisitDaysCount = daysCount;
            _viewModel.IsEditMode = isEditMode;

            // تحميل العروض المحفوظة
            _ = _viewModel.LoadSavedOffers(visitNumber);

            // تحديث عنوان النافذة
            if (isEditMode)
            {
                this.Title = $"تعديل عروض الأسعار - الزيارة {visitNumber}";
            }
        }

        /// <summary>
        /// منشئ لوضع التعديل مع البيانات الموجودة - ينقل البيانات المعروضة مباشرة
        /// </summary>
        /// <param name="existingOffers">العروض الموجودة</param>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="daysCount">عدد الأيام</param>
        /// <param name="isEditMode">وضع التعديل</param>
        public OffersWindow(List<DriverOffer> existingOffers, string visitNumber, int daysCount, bool isEditMode = true)
        {
            try
            {
                InitializeComponent();
                InitializeViewModel();

                System.Diagnostics.Debug.WriteLine($"🔧 Creating OffersWindow with existing offers for visit: {visitNumber}");
                System.Diagnostics.Debug.WriteLine($"📊 Received {existingOffers?.Count ?? 0} existing offers");

                // تحديد معلومات الزيارة
                _viewModel.VisitNumber = visitNumber;
                _viewModel.VisitDaysCount = daysCount;
                _viewModel.IsEditMode = isEditMode;

                // تحديث عنوان النافذة
                this.Title = $"تعديل عروض الأسعار - الزيارة {visitNumber}";

                if (existingOffers?.Any() == true)
                {
                    // تحميل البيانات الموجودة مباشرة
                    LoadExistingOffers(existingOffers);
                }
                else
                {
                    // إذا لم توجد بيانات، محاولة تحميل العروض المحفوظة من قاعدة البيانات
                    System.Diagnostics.Debug.WriteLine($"🔄 لا توجد بيانات موجودة، محاولة تحميل من قاعدة البيانات للزيارة: {visitNumber}");
                    // سيتم التحميل في Window_Loaded
                }

                System.Diagnostics.Debug.WriteLine($"✅ OffersWindow created with {existingOffers?.Count ?? 0} existing offers");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error creating OffersWindow with existing offers: {ex.Message}");
                MessageBox.Show($"خطأ في إنشاء نافذة التعديل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل البيانات الموجودة مباشرة إلى النافذة
        /// </summary>
        /// <param name="existingOffers">العروض الموجودة</param>
        private void LoadExistingOffers(List<DriverOffer> existingOffers)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 Loading {existingOffers.Count} existing offers into OffersWindow");

                // مسح البيانات الحالية
                _viewModel.DriverOffers.Clear();

                // ترتيب العروض حسب السعر قبل الإضافة
                var sortedOffers = existingOffers.OrderBy(o => o.ProposedAmount).ToList();

                // إضافة البيانات الموجودة مرتبة
                foreach (var offer in sortedOffers)
                {
                    _viewModel.DriverOffers.Add(offer);
                    System.Diagnostics.Debug.WriteLine($"✅ Loaded offer: {offer.DriverName} - {offer.FormattedAmount} - Status: {offer.OfferStatus}");
                }

                // تطبيق فلترة أقل الأسعار تلقائياً إذا لم تكن هناك عروض محددة مسبقاً
                var hasSelectedOffers = sortedOffers.Any(o => o.IsSelected);
                if (!hasSelectedOffers)
                {
                    System.Diagnostics.Debug.WriteLine("🎯 No pre-selected offers found, applying auto-filter for lowest prices");
                    _viewModel.FilterByPriceCommand.Execute();
                }

                System.Diagnostics.Debug.WriteLine($"🎯 Successfully loaded {existingOffers.Count} offers into edit mode with auto-filtering");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error loading existing offers: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل البيانات الموجودة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تهيئة ViewModel
        /// </summary>
        private void InitializeViewModel(IDataService dataService = null)
        {
            try
            {
                // استخدام خدمة البيانات المرسلة أو إنشاء واحدة جديدة
                var service = dataService ?? new DatabaseService();
                
                _viewModel = new OffersViewModel(service);
                DataContext = _viewModel;

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة OffersWindow بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة OffersWindow: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // يمكن إضافة تأكيد الحفظ هنا
                var result = MessageBox.Show(
                    "هل تريد إغلاق النافذة؟\n\nتأكد من حفظ العروض قبل الإغلاق.",
                    "تأكيد الإغلاق",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    DialogResult = false;
                    Close();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إغلاق النافذة: {ex.Message}");
                Close(); // إغلاق اضطراري
            }
        }

        /// <summary>
        /// معالج انتهاء تحرير الخلية - لحفظ تغييرات الحالة
        /// </summary>
        private async void OffersDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            try
            {
                // التحقق من أن التحرير في عمود الحالة
                if (e.Column.Header.ToString() == "الحالة")
                {
                    var offer = e.Row.Item as DriverOffer;
                    if (offer != null && !string.IsNullOrEmpty(offer.VisitNumber))
                    {
                        // الحصول على القيمة الجديدة
                        var comboBox = e.EditingElement as ComboBox;
                        if (comboBox != null && comboBox.SelectedItem != null)
                        {
                            var newStatus = comboBox.SelectedItem.ToString();
                            await UpdateOfferStatus(offer, newStatus);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالج تحرير الخلية: {ex.Message}");
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث حالة العرض في قاعدة البيانات
        /// </summary>
        private async Task UpdateOfferStatus(DriverOffer offer, string newStatus)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 محاولة تحديث حالة السائق {offer.DriverName} إلى {newStatus}");
                System.Diagnostics.Debug.WriteLine($"🔍 رقم الزيارة: {offer.VisitNumber}");

                // تحديث الحالة في قاعدة البيانات
                using var context = new ApplicationDbContext();
                var offersService = new OffersService(context);
                var success = await offersService.UpdateOfferStatusAsync(
                    offer.DriverName,
                    offer.VisitNumber,
                    newStatus);

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحديث حالة السائق {offer.DriverName} إلى {newStatus}");

                    // تحديث الخصائص المحلية
                    offer.OfferStatus = newStatus;
                    if (newStatus == "🏆 فائز")
                    {
                        offer.IsWinner = true;
                        offer.IsSelected = true;
                    }
                    else if (newStatus == "😔 اعتذر")
                    {
                        offer.IsWinner = false;
                        offer.IsSelected = false;
                    }

                    // إشعار بالتحديث الناجح
                    MessageBox.Show($"تم تحديث حالة السائق {offer.DriverName} إلى {newStatus}",
                                  "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في تحديث حالة السائق {offer.DriverName}");
                    MessageBox.Show($"فشل في تحديث حالة السائق {offer.DriverName}",
                                  "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث الحالة: {ex.Message}");
                MessageBox.Show($"خطأ في تحديث الحالة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج تحميل النافذة
        /// </summary>
        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحديث العنوان
                if (!string.IsNullOrEmpty(_viewModel?.VisitNumber))
                {
                    Title = $"عروض الأسعار - الزيارة رقم {_viewModel.VisitNumber}";
                }

                // إذا كانت النافذة في وضع التعديل ولا توجد عروض محملة، تحميل العروض المحفوظة
                if (_viewModel?.IsEditMode == true && !string.IsNullOrEmpty(_viewModel.VisitNumber) &&
                    (_viewModel.DriverOffers?.Count ?? 0) == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"🔄 تحميل العروض المحفوظة للزيارة: {_viewModel.VisitNumber}");
                    await _viewModel.LoadSavedOffers(_viewModel.VisitNumber);
                }

                System.Diagnostics.Debug.WriteLine("✅ تم تحميل OffersWindow");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل النافذة: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل النافذة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // التحقق من وجود تغييرات غير محفوظة
                if (_viewModel?.DriverOffers?.Count > 0)
                {
                    var hasSelectedOffers = false;
                    foreach (var offer in _viewModel.DriverOffers)
                    {
                        if (offer.IsSelected)
                        {
                            hasSelectedOffers = true;
                            break;
                        }
                    }

                    if (hasSelectedOffers)
                    {
                        var result = MessageBox.Show(
                            "يوجد عروض محددة لم يتم حفظها.\n\nهل تريد المتابعة بدون حفظ؟",
                            "تحذير",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Warning);

                        if (result == MessageBoxResult.No)
                        {
                            e.Cancel = true;
                            return;
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إغلاق OffersWindow");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إغلاق النافذة: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على العروض المختارة
        /// </summary>
        /// <returns>قائمة العروض المختارة</returns>
        public ObservableCollection<DriverOffer> GetSelectedOffers()
        {
            var selectedOffers = new ObservableCollection<DriverOffer>();
            
            if (_viewModel?.DriverOffers != null)
            {
                foreach (var offer in _viewModel.DriverOffers)
                {
                    if (offer.IsSelected)
                    {
                        selectedOffers.Add(offer);
                    }
                }
            }

            return selectedOffers;
        }

        /// <summary>
        /// الحصول على السائق الفائز
        /// </summary>
        /// <returns>السائق الفائز أو null</returns>
        public DriverOffer GetWinnerOffer()
        {
            if (_viewModel?.DriverOffers != null)
            {
                foreach (var offer in _viewModel.DriverOffers)
                {
                    if (offer.IsWinner)
                    {
                        return offer;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// تحديث قائمة السائقين
        /// </summary>
        /// <param name="drivers">قائمة السائقين الجديدة</param>
        public async void UpdateDriversList(ObservableCollection<Driver> drivers)
        {
            if (_viewModel != null)
            {
                await _viewModel.LoadDriverOffers(drivers);
            }
        }

        /// <summary>
        /// تحديث معلومات الزيارة
        /// </summary>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="daysCount">عدد الأيام</param>
        public void UpdateVisitInfo(string visitNumber, int daysCount)
        {
            if (_viewModel != null)
            {
                _viewModel.VisitNumber = visitNumber;
                _viewModel.VisitDaysCount = daysCount;
                
                // تحديث العنوان
                Title = $"عروض الأسعار - الزيارة رقم {visitNumber}";
            }
        }
    }
}
