using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class LoginWindow : Window
    {
        private readonly LoginViewModel _viewModel;
        private DispatcherTimer? _clockTimer;

        public LoginWindow()
        {
            InitializeComponent();

            var authService = new AuthenticationService();
            _viewModel = new LoginViewModel(authService);
            DataContext = _viewModel;

            _viewModel.LoginCompleted += OnLoginCompleted;

            // Initialize timer for clock and last login update
            _clockTimer = new DispatcherTimer();
            _clockTimer.Interval = TimeSpan.FromSeconds(1);
            _clockTimer.Tick += UpdateClockAndLastLogin;
            _clockTimer.Start();

            // Bind password box to view model
            PasswordBox.PasswordChanged += (s, e) =>
            {
                _viewModel.Password = PasswordBox.Password;
            };

            // Set focus to username textbox
            Loaded += (s, e) =>
            {
                UsernameTextBox.Focus();
                // Set default values for testing
                UsernameTextBox.Text = "admin";
                PasswordBox.Password = "123456";
                _viewModel.Username = "admin";
                _viewModel.Password = "123456";

                // Update clock and last login time
                UpdateClockAndLastLogin(null, null);
            };
        }

        private void LoginButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            // Show loading indicator
            MainLoginButton.IsEnabled = false;
            LoadingPanel.Visibility = Visibility.Visible;

            // Update password from PasswordBox
            _viewModel.Password = PasswordBox.Password;

            // Execute login command
            if (_viewModel.LoginCommand.CanExecute())
            {
                _viewModel.LoginCommand.Execute();
            }

            // Simulate login delay for better UX
            var timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1.5);
            timer.Tick += (s, args) =>
            {
                timer.Stop();
                MainLoginButton.IsEnabled = true;
                LoadingPanel.Visibility = Visibility.Collapsed;
            };
            timer.Start();
        }

        private void UpdateClockAndLastLogin(object sender, EventArgs e)
        {
            // Update digital clock
            var now = DateTime.Now;
            var arabicClock = now.ToString("dddd، dd MMMM yyyy - hh:mm:ss tt",
                new System.Globalization.CultureInfo("ar-SA"));
            ClockText.Text = arabicClock;

            // Get last login from settings or default
            var lastLogin = Properties.Settings.Default.LastLoginTime;
            if (lastLogin == DateTime.MinValue)
            {
                lastLogin = DateTime.Now.AddDays(-1); // Default to yesterday
            }

            // Format the last login date in Arabic
            var arabicDate = lastLogin.ToString("dddd، dd MMMM yyyy - hh:mm tt",
                new System.Globalization.CultureInfo("ar-SA"));

            LastLoginText.Text = arabicDate;
        }

        private void OnLoginCompleted(object? sender, bool success)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔐 استلام نتيجة تسجيل الدخول: {success}");

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("✅ حفظ وقت آخر تسجيل دخول");
                    // Save last login time
                    Properties.Settings.Default.LastLoginTime = DateTime.Now;
                    Properties.Settings.Default.Save();

                    // Show success message briefly
                    LoadingPanel.Visibility = Visibility.Collapsed;
                    var successPanel = new StackPanel
                    {
                        Orientation = System.Windows.Controls.Orientation.Horizontal,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };
                    successPanel.Children.Add(new TextBlock { Text = "✅", FontSize = 16, Margin = new Thickness(0, 0, 8, 0) });
                    successPanel.Children.Add(new TextBlock { Text = "تم تسجيل الدخول بنجاح!", FontSize = 14, Foreground = System.Windows.Media.Brushes.Green });

                    System.Diagnostics.Debug.WriteLine("🏠 إنشاء النافذة الرئيسية");

                    try
                    {
                        System.Diagnostics.Debug.WriteLine("🏠 إنشاء وعرض النافذة الرئيسية");

                        // إيقاف مؤقت الساعة أولاً
                        _clockTimer?.Stop();
                        _clockTimer = null;

                        // إنشاء النافذة الرئيسية
                        var mainWindow = new MainWindow();

                        // جعل النافذة الرئيسية النافذة الرئيسية للتطبيق قبل إغلاق LoginWindow
                        Application.Current.MainWindow = mainWindow;

                        // عرض النافذة الرئيسية
                        mainWindow.Show();

                        // تعيين DialogResult لإنهاء ShowDialog بنجاح
                        this.DialogResult = true;

                        System.Diagnostics.Debug.WriteLine("✅ تم الانتقال بنجاح إلى النافذة الرئيسية");
                    }
                    catch (Exception mainEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ الخطأ المحدد: {mainEx.Message}");
                        System.Diagnostics.Debug.WriteLine($"Stack Trace: {mainEx.StackTrace}");

                        MessageBox.Show($"❌ خطأ في تسجيل الدخول:\n\n{mainEx.Message}",
                            "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);

                        // إعادة تمكين زر تسجيل الدخول
                        MainLoginButton.IsEnabled = true;
                        LoadingPanel.Visibility = Visibility.Collapsed;
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل تسجيل الدخول");
                    // Hide loading and show error
                    MainLoginButton.IsEnabled = true;
                    LoadingPanel.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في OnLoginCompleted: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.ToString()}");
                MessageBox.Show($"خطأ في فتح النافذة الرئيسية: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.ToString()}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);

                // Re-enable login button
                MainLoginButton.IsEnabled = true;
                LoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        protected override void OnClosed(System.EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إغلاق LoginWindow");

                if (_viewModel != null)
                {
                    _viewModel.LoginCompleted -= OnLoginCompleted;
                    System.Diagnostics.Debug.WriteLine("✅ تم إلغاء تسجيل أحداث LoginViewModel");
                }

                if (_clockTimer != null)
                {
                    _clockTimer.Stop();
                    _clockTimer = null;
                    System.Diagnostics.Debug.WriteLine("✅ تم إيقاف وتنظيف مؤقت الساعة");
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إغلاق LoginWindow بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إغلاق LoginWindow: {ex.Message}");
            }
            finally
            {
                try
                {
                    base.OnClosed(e);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في base.OnClosed: {ex.Message}");
                }
            }
        }
    }
}
