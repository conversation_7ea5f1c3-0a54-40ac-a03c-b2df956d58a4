using System;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// واجهة الوصول السريع - عرض السائقين المتواجدين بالميدان
    /// </summary>
    public partial class QuickAccessView : UserControl
    {
        private readonly QuickAccessViewModel _viewModel;

        public QuickAccessView()
        {
            InitializeComponent();

            _viewModel = new QuickAccessViewModel();
            DataContext = _viewModel;

            // إعداد الواجهة
            SetupView();

            System.Diagnostics.Debug.WriteLine("🚀 تم تحميل واجهة الوصول السريع");
        }

        /// <summary>
        /// إعداد خصائص الواجهة
        /// </summary>
        private void SetupView()
        {
            try
            {
                // يمكن إضافة أي إعدادات خاصة بالواجهة هنا
                System.Diagnostics.Debug.WriteLine("✅ تم إعداد واجهة الوصول السريع بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد الواجهة: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج تحميل الواجهة
        /// </summary>
        private void UserControl_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                // يمكن إضافة أي إعدادات إضافية هنا
                System.Diagnostics.Debug.WriteLine("📋 تم تحميل واجهة الوصول السريع");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل الواجهة: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج إلغاء تحميل الواجهة لتنظيف الموارد
        /// </summary>
        private void UserControl_Unloaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                // تنظيف الموارد
                _viewModel?.Dispose();
                
                System.Diagnostics.Debug.WriteLine("🧹 تم تنظيف موارد واجهة الوصول السريع");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف الموارد: {ex.Message}");
            }
        }
    }
}
